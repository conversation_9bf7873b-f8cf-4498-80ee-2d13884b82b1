package model

import "time"

// TaskExecutionReport 任务执行报表数据模型（ccserver版本）
type TaskExecutionReport struct {
	Id int64 `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`

	// 基础标识字段
	TaskId      int64  `json:"task_id" gorm:"not null;comment:主任务ID"`
	ChildId     int    `json:"child_id" gorm:"not null;comment:子任务ID"`
	ExecutionNo int    `json:"execution_no" gorm:"not null;comment:执行序号"`
	Imsi        string `json:"imsi" gorm:"size:50;not null;comment:设备标识"`

	// 核心时间字段
	RunningTask   string    `json:"running_task" gorm:"size:100;comment:运行任务"`
	DepartureTime time.Time `json:"departure_time" gorm:"comment:实际出发时间"`
	ArrivalTime   time.Time `json:"arrival_time" gorm:"comment:实际到达时间"`
	EndTime       time.Time `json:"end_time" gorm:"comment:任务结束时间"`

	// 状态字段
	Status int `json:"status" gorm:"default:1;comment:执行状态: 0=不执行, 1=将要执行, 2=正在执行, 3=已完成, 4=已暂停, 5=已取消, 6=已终止, 7=无法完成"`

	// 车辆信息字段
	VehiclePlate string `json:"vehicle_plate" gorm:"size:50;comment:车牌"`
	VehicleType  string `json:"vehicle_type" gorm:"size:50;comment:车型"`
	VehicleUsage string `json:"vehicle_usage" gorm:"size:50;comment:任务车辆用途"`

	// 任务信息字段
	TaskType int `json:"task_type" gorm:"default:0;comment:任务类型: 0=点位任务, 1=操作任务, 2=车道任务, 3=区域任务"`

	// 里程和性能字段
	PlannedDistanceM   int     `json:"planned_distance_m" gorm:"default:0;comment:理论规划里程(M)"`
	PlannedDurationMin int     `json:"planned_duration_min" gorm:"default:0;comment:计划时长(min)"`
	ActualDurationMin  int     `json:"actual_duration_min" gorm:"default:0;comment:实际时长(min)"`
	StartMileageM      int     `json:"start_mileage_m" gorm:"default:0;comment:开始里程值(M)"`
	EndMileageM        int     `json:"end_mileage_m" gorm:"default:0;comment:结束里程值(M)"`
	StartCM            int     `json:"start_cm" gorm:"default:0;comment:开始CM值"`
	EndCM              int     `json:"end_cm" gorm:"default:0;comment:结束CM值"`
	ActualMileageM     int     `json:"actual_mileage_m" gorm:"default:0;comment:实际运行里程(M)"`
	ActualSpeedKmh     float64 `json:"actual_speed_kmh" gorm:"type:decimal(8,2);default:0;comment:实际运行速度(KM/h)"`

	// 人员和地点信息
	Creator       string `json:"creator" gorm:"size:100;comment:任务创建人"`
	StartLocation string `json:"start_location" gorm:"size:200;comment:出发点名称"`
	EndLocation   string `json:"end_location" gorm:"size:200;comment:目的地点名称"`
	Waypoints     string `json:"waypoints" gorm:"type:text;comment:途径点"`

	// 扩展字段
	ExecutionHash string `json:"execution_hash" gorm:"size:100;comment:执行唯一标识"`

	// 原始数据字段
	OriginalTaskContent  string `json:"original_task_content" gorm:"type:text;comment:原始任务内容JSON"`
	HeartbeatDataSummary string `json:"heartbeat_data_summary" gorm:"type:text;comment:心跳数据摘要JSON"`

	// 终止原因字段
	TerminationReason string `json:"termination_reason" gorm:"size:500;comment:任务终止原因"`

	// 时间戳字段
	CreatedTime int64 `json:"created_time" gorm:"not null;comment:创建时间戳"`
	UpdatedTime int64 `json:"updated_time" gorm:"comment:更新时间戳"`
}

// TableName 指定表名
func (TaskExecutionReport) TableName() string {
	return "task_execution_report"
}

// TaskStatusMap 任务状态映射
var TaskStatusMap = map[int]string{
	0: "不执行",
	1: "将要执行",
	2: "正在执行",
	3: "已完成",
	4: "已暂停",
	5: "已取消",
	6: "已终止",
	7: "无法完成",
}

// VehicleTypeMap 车型映射
var VehicleTypeMap = map[int]string{
	0: "RoboBus",
	1: "清扫车",
	2: "物流车",
	3: "室内清洁车",
	4: "海森堡",
}

// VehicleUsageMap 车辆用途映射
var VehicleUsageMap = map[int]string{
	0: "公交运输",
	1: "清扫作业",
	2: "物流运输",
	3: "室内清洁",
	4: "特殊用途",
}

// TaskTypeMap 任务类型映射
var TaskTypeMap = map[int]string{
	1: "定时任务",
	2: "周期任务",
}
