package websocket

import (
	"ccserver/app/service/tdengine"
	"ccserver/pix_log"
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"runtime"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
	"github.com/google/uuid"
)

// 🚀 性能监控指标
type Metrics struct {
	ConnectedClients    int64     `json:"connected_clients"`
	MessagesPerSecond   int64     `json:"messages_per_second"`
	TotalMessages       int64     `json:"total_messages"`
	MemoryUsage         int64     `json:"memory_usage"`
	GoroutineCount      int64     `json:"goroutine_count"`
	ConnectionsAdded    int64     `json:"connections_added"`
	ConnectionsRemoved  int64     `json:"connections_removed"`
	LastUpdateTime      time.Time `json:"last_update_time"`
}

// 🔧 对象池 - 消息缓冲区复用
var messagePool = sync.Pool{
	New: func() interface{} {
		return make([]byte, 0, 1024) // 预分配1KB缓冲区
	},
}

// 🔧 对象池 - ClientState复用
var clientStatePool = sync.Pool{
	New: func() interface{} {
		return &ClientState{
			Filters: make(map[string]interface{}),
		}
	},
}

// ClientState 客户端状态
type ClientState struct {
	SessionID     string                 `json:"session_id"`
	CurrentPage   int                    `json:"current_page"`
	PageSize      int                    `json:"page_size"`
	Filters       map[string]interface{} `json:"filters"`
	LastUpdate    time.Time              `json:"last_update"`
	ConnectedTime time.Time              `json:"connected_time"`
	LastHeartbeat time.Time              `json:"last_heartbeat"` // 🔧 添加心跳时间
	Connection    *websocket.Conn        `json:"-"`
	WriteMutex    sync.Mutex             `json:"-"` // 🔧 写入锁，防止并发写入
	IsActive      int32                  `json:"-"` // 🔧 原子操作标记连接是否活跃
}

// ClientStateInfo 客户端状态信息（用于前端显示）
type ClientStateInfo struct {
	SessionID     string                 `json:"session_id"`
	CurrentPage   int                    `json:"current_page"`
	PageSize      int                    `json:"page_size"`
	Filters       map[string]interface{} `json:"filters"`
	LastUpdate    time.Time              `json:"last_update"`
	ConnectedTime time.Time              `json:"connected_time"`
	Duration      string                 `json:"duration"`
}

// 🚀 分片锁机制 - 客户端分片
type ClientShard struct {
	clients map[*websocket.Conn]*ClientState
	mutex   sync.RWMutex
	count   int64 // 原子计数器
}

// 🚀 优化后的WebSocketService
type OptimizedWebSocketService struct {
	// 🔧 分片锁机制，减少锁竞争
	shards     []*ClientShard
	shardCount int
	shardMask  uint32

	// 🔧 性能监控
	metrics *Metrics

	// 🔧 系统组件
	tdengineClient *tdengine.TDengineClient
	
	// 🔧 控制通道
	stopChan       chan bool
	cleanupTicker  *time.Ticker
	metricsTicker  *time.Ticker
	
	// 🔧 状态管理
	isRunning      int32 // 原子操作
	
	// 🔧 超时配置
	heartbeatTimeout time.Duration
	writeTimeout     time.Duration
	readTimeout      time.Duration
}

// 🚀 NewOptimizedWebSocketService 创建优化后的WebSocket服务
func NewOptimizedWebSocketService() *OptimizedWebSocketService {
	pix_log.Info("[WEBSOCKET] 🚀 创建优化版WebSocket服务实例")

	// 🔧 计算分片数量（基于CPU核心数）
	shardCount := runtime.NumCPU() * 2 // 每个CPU核心2个分片
	if shardCount < 4 {
		shardCount = 4 // 最少4个分片
	}
	if shardCount > 64 {
		shardCount = 64 // 最多64个分片
	}

	pix_log.Info("[WEBSOCKET] 🔧 分片数量: %d (基于%d个CPU核心)", shardCount, runtime.NumCPU())

	// 🔧 创建分片
	shards := make([]*ClientShard, shardCount)
	for i := 0; i < shardCount; i++ {
		shards[i] = &ClientShard{
			clients: make(map[*websocket.Conn]*ClientState),
			count:   0,
		}
	}

	// 创建TDengine客户端
	client := tdengine.NewTDengineClient()
	pix_log.Info("[WEBSOCKET] TDengine客户端创建成功")

	service := &OptimizedWebSocketService{
		shards:     shards,
		shardCount: shardCount,
		shardMask:  uint32(shardCount - 1), // 用于快速取模运算
		
		metrics: &Metrics{
			LastUpdateTime: time.Now(),
		},
		
		tdengineClient: client,
		stopChan:       make(chan bool),
		isRunning:      0,
		
		// 🔧 超时配置
		heartbeatTimeout: 5 * time.Minute,  // 5分钟心跳超时
		writeTimeout:     30 * time.Second, // 30秒写入超时
		readTimeout:      60 * time.Second, // 60秒读取超时
	}

	// 🚀 启动后台任务
	service.startBackgroundTasks()

	pix_log.Info("[WEBSOCKET] ✅ 优化版WebSocket服务实例创建完成")
	return service
}

// 🚀 分片哈希函数 - 基于连接地址计算分片
func (s *OptimizedWebSocketService) getShardIndex(conn *websocket.Conn) int {
	h := fnv.New32a()
	h.Write([]byte(conn.RemoteAddr().String()))
	return int(h.Sum32() & s.shardMask)
}

// 🚀 获取连接对应的分片
func (s *OptimizedWebSocketService) getShard(conn *websocket.Conn) *ClientShard {
	index := s.getShardIndex(conn)
	return s.shards[index]
}

// 🚀 startBackgroundTasks 启动后台任务
func (s *OptimizedWebSocketService) startBackgroundTasks() {
	if !atomic.CompareAndSwapInt32(&s.isRunning, 0, 1) {
		return // 已经在运行
	}

	pix_log.Info("[WEBSOCKET] 🚀 启动后台任务")

	// 🔧 连接清理任务 - 每30秒执行一次
	s.cleanupTicker = time.NewTicker(30 * time.Second)
	go s.cleanupTask()

	// 🔧 性能监控任务 - 每10秒执行一次
	s.metricsTicker = time.NewTicker(10 * time.Second)
	go s.metricsTask()

	pix_log.Info("[WEBSOCKET] ✅ 后台任务启动完成")
}

// getClientIP 从连接中提取客户端IP地址（去除端口号）
func (s *OptimizedWebSocketService) getClientIP(conn *websocket.Conn) string {
	addr := conn.RemoteAddr().String()
	// 对于IPv6地址，格式可能是 [::1]:port
	// 对于IPv4地址，格式是 ip:port
	if strings.HasPrefix(addr, "[") {
		// IPv6地址
		if idx := strings.LastIndex(addr, "]:"); idx != -1 {
			return addr[1:idx] // 去掉开头的 [ 和结尾的 ]:port
		}
	} else {
		// IPv4地址
		if idx := strings.LastIndex(addr, ":"); idx != -1 {
			return addr[:idx] // 去掉 :port
		}
	}
	return addr // 如果解析失败，返回原始地址
}

// formatDuration 格式化持续时间
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0f秒", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.0f分钟", d.Minutes())
	} else if d < 24*time.Hour {
		return fmt.Sprintf("%.1f小时", d.Hours())
	} else {
		return fmt.Sprintf("%.1f天", d.Hours()/24)
	}
}

// 🚀 优化后的AddClient - 使用分片锁
func (s *OptimizedWebSocketService) AddClient(conn *websocket.Conn) {
	startTime := time.Now()

	// 🔧 预先准备数据，减少锁持有时间
	clientIP := s.getClientIP(conn)
	sessionID := uuid.New().String()
	now := time.Now()

	// 🔧 从对象池获取ClientState
	clientState := clientStatePool.Get().(*ClientState)
	clientState.SessionID = sessionID
	clientState.CurrentPage = 1
	clientState.PageSize = 10
	clientState.LastUpdate = now
	clientState.ConnectedTime = now
	clientState.LastHeartbeat = now
	clientState.Connection = conn
	atomic.StoreInt32(&clientState.IsActive, 1)

	// 清空Filters map
	for k := range clientState.Filters {
		delete(clientState.Filters, k)
	}

	// 🚀 获取对应的分片
	shard := s.getShard(conn)

	// 🔧 最小化锁持有时间
	shard.mutex.Lock()

	// 检查连接是否已存在
	if existingState, exists := shard.clients[conn]; exists {
		pix_log.Warning("[WEBSOCKET] ⚠️ 连接已存在，SessionID: %s", existingState.SessionID)
	}

	// 添加到分片
	shard.clients[conn] = clientState
	atomic.AddInt64(&shard.count, 1)

	shard.mutex.Unlock()

	// 🔧 更新全局指标
	atomic.AddInt64(&s.metrics.ConnectedClients, 1)
	atomic.AddInt64(&s.metrics.ConnectionsAdded, 1)

	// 🔧 简化日志输出，避免性能损耗
	pix_log.Info("[WEBSOCKET] ✅ 新客户端连接 SessionID: %s, IP: %s, 耗时: %v",
		sessionID, clientIP, time.Since(startTime))

	// 🚀 异步广播状态更新，避免死锁
	go func() {
		defer func() {
			if r := recover(); r != nil {
				pix_log.Error("[WEBSOCKET] 💥 广播状态更新异常: %v", r)
			}
		}()

		// 使用超时上下文防止长时间阻塞
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		s.sendClientStatesUpdateWithContext(ctx)
	}()
}

// 🚀 优化后的RemoveClient - 使用分片锁
func (s *OptimizedWebSocketService) RemoveClient(conn *websocket.Conn) {
	startTime := time.Now()

	// 🚀 获取对应的分片
	shard := s.getShard(conn)

	var clientState *ClientState
	var sessionID string

	// 🔧 最小化锁持有时间
	shard.mutex.Lock()

	if state, exists := shard.clients[conn]; exists {
		clientState = state
		sessionID = state.SessionID

		// 标记为非活跃
		atomic.StoreInt32(&state.IsActive, 0)

		// 从分片中移除
		delete(shard.clients, conn)
		atomic.AddInt64(&shard.count, -1)
	}

	shard.mutex.Unlock()

	if clientState != nil {
		// 🔧 安全关闭连接
		func() {
			defer func() {
				if r := recover(); r != nil {
					pix_log.Error("[WEBSOCKET] 💥 关闭连接异常: %v", r)
				}
			}()
			conn.Close()
		}()

		// 🔧 回收ClientState到对象池
		clientStatePool.Put(clientState)

		// 🔧 更新全局指标
		atomic.AddInt64(&s.metrics.ConnectedClients, -1)
		atomic.AddInt64(&s.metrics.ConnectionsRemoved, 1)

		pix_log.Info("[WEBSOCKET] ✅ 客户端断开连接 SessionID: %s, 耗时: %v",
			sessionID, time.Since(startTime))

		// 🚀 异步广播状态更新
		go func() {
			defer func() {
				if r := recover(); r != nil {
					pix_log.Error("[WEBSOCKET] 💥 广播状态更新异常: %v", r)
				}
			}()

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			s.sendClientStatesUpdateWithContext(ctx)
		}()
	} else {
		pix_log.Warning("[WEBSOCKET] ⚠️ 要移除的连接不存在: %s", conn.RemoteAddr().String())
	}
}

// 🚀 cleanupTask 连接清理任务
func (s *OptimizedWebSocketService) cleanupTask() {
	defer func() {
		if r := recover(); r != nil {
			pix_log.Error("[WEBSOCKET] 💥 清理任务异常: %v", r)
		}
	}()

	for {
		select {
		case <-s.cleanupTicker.C:
			s.cleanupTimeoutConnections()
		case <-s.stopChan:
			pix_log.Info("[WEBSOCKET] 🛑 清理任务停止")
			return
		}
	}
}

// 🚀 metricsTask 性能监控任务
func (s *OptimizedWebSocketService) metricsTask() {
	defer func() {
		if r := recover(); r != nil {
			pix_log.Error("[WEBSOCKET] 💥 监控任务异常: %v", r)
		}
	}()

	for {
		select {
		case <-s.metricsTicker.C:
			s.updateMetrics()
		case <-s.stopChan:
			pix_log.Info("[WEBSOCKET] 🛑 监控任务停止")
			return
		}
	}
}

// 🚀 cleanupTimeoutConnections 清理超时连接
func (s *OptimizedWebSocketService) cleanupTimeoutConnections() {
	now := time.Now()
	timeoutConnections := make([]*websocket.Conn, 0)

	// 🔧 遍历所有分片查找超时连接
	for _, shard := range s.shards {
		shard.mutex.RLock()
		for conn, state := range shard.clients {
			if now.Sub(state.LastHeartbeat) > s.heartbeatTimeout {
				timeoutConnections = append(timeoutConnections, conn)
			}
		}
		shard.mutex.RUnlock()
	}

	// 🔧 清理超时连接
	if len(timeoutConnections) > 0 {
		pix_log.Info("[WEBSOCKET] 🧹 清理 %d 个超时连接", len(timeoutConnections))
		for _, conn := range timeoutConnections {
			s.RemoveClient(conn)
		}
	}
}

// 🚀 updateMetrics 更新性能指标
func (s *OptimizedWebSocketService) updateMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 🔧 计算总连接数
	var totalConnections int64
	for _, shard := range s.shards {
		totalConnections += atomic.LoadInt64(&shard.count)
	}

	// 🔧 更新指标
	atomic.StoreInt64(&s.metrics.ConnectedClients, totalConnections)
	atomic.StoreInt64(&s.metrics.MemoryUsage, int64(m.Alloc))
	atomic.StoreInt64(&s.metrics.GoroutineCount, int64(runtime.NumGoroutine()))
	s.metrics.LastUpdateTime = time.Now()

	// 🔧 定期输出关键指标
	if time.Now().Unix()%60 == 0 { // 每分钟输出一次
		pix_log.Info("[WEBSOCKET] 📊 指标 - 连接数: %d, 内存: %dMB, 协程: %d",
			totalConnections,
			m.Alloc/1024/1024,
			runtime.NumGoroutine())
	}
}

// 🚀 GetClientCount 优化后的获取客户端连接数
func (s *OptimizedWebSocketService) GetClientCount() int {
	// 🔧 使用原子操作快速获取连接数，避免锁竞争
	return int(atomic.LoadInt64(&s.metrics.ConnectedClients))
}

// 🚀 GetDetailedClientCount 获取详细的客户端连接数（包含分片信息）
func (s *OptimizedWebSocketService) GetDetailedClientCount() map[string]interface{} {
	shardCounts := make([]int64, len(s.shards))
	var totalCount int64

	// 🔧 快速收集各分片连接数
	for i, shard := range s.shards {
		count := atomic.LoadInt64(&shard.count)
		shardCounts[i] = count
		totalCount += count
	}

	return map[string]interface{}{
		"total_connections": totalCount,
		"shard_counts":     shardCounts,
		"shard_count":      len(s.shards),
		"timestamp":        time.Now().Format("2006-01-02 15:04:05.000"),
	}
}

// 🚀 GetMetrics 获取性能指标
func (s *OptimizedWebSocketService) GetMetrics() *Metrics {
	// 🔧 返回指标副本，避免并发访问问题
	return &Metrics{
		ConnectedClients:   atomic.LoadInt64(&s.metrics.ConnectedClients),
		MessagesPerSecond:  atomic.LoadInt64(&s.metrics.MessagesPerSecond),
		TotalMessages:      atomic.LoadInt64(&s.metrics.TotalMessages),
		MemoryUsage:        atomic.LoadInt64(&s.metrics.MemoryUsage),
		GoroutineCount:     atomic.LoadInt64(&s.metrics.GoroutineCount),
		ConnectionsAdded:   atomic.LoadInt64(&s.metrics.ConnectionsAdded),
		ConnectionsRemoved: atomic.LoadInt64(&s.metrics.ConnectionsRemoved),
		LastUpdateTime:     s.metrics.LastUpdateTime,
	}
}

// 🚀 GetClientStates 优化后的获取所有客户端状态信息
func (s *OptimizedWebSocketService) GetClientStates() []ClientStateInfo {
	now := time.Now()
	var clientStates []ClientStateInfo

	// 🔧 并发收集各分片的客户端状态，减少锁持有时间
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, shard := range s.shards {
		wg.Add(1)
		go func(shard *ClientShard) {
			defer wg.Done()

			var shardStates []ClientStateInfo

			// 🔧 最小化锁持有时间
			shard.mutex.RLock()
			for _, clientState := range shard.clients {
				// 🔧 只有活跃连接才包含在结果中
				if atomic.LoadInt32(&clientState.IsActive) == 1 {
					duration := now.Sub(clientState.ConnectedTime)
					durationStr := formatDuration(duration)

					// 🔧 复制Filters避免并发访问问题
					filtersCopy := make(map[string]interface{})
					for k, v := range clientState.Filters {
						filtersCopy[k] = v
					}

					info := ClientStateInfo{
						SessionID:     clientState.SessionID,
						CurrentPage:   clientState.CurrentPage,
						PageSize:      clientState.PageSize,
						Filters:       filtersCopy,
						LastUpdate:    clientState.LastUpdate,
						ConnectedTime: clientState.ConnectedTime,
						Duration:      durationStr,
					}
					shardStates = append(shardStates, info)
				}
			}
			shard.mutex.RUnlock()

			// 🔧 合并结果
			if len(shardStates) > 0 {
				mu.Lock()
				clientStates = append(clientStates, shardStates...)
				mu.Unlock()
			}
		}(shard)
	}

	wg.Wait()

	pix_log.Info("[WEBSOCKET] 📊 获取客户端状态完成，返回 %d 个客户端", len(clientStates))
	return clientStates
}

// 🚀 sendClientStatesUpdateWithContext 带超时的客户端状态更新
func (s *OptimizedWebSocketService) sendClientStatesUpdateWithContext(ctx context.Context) {
	select {
	case <-ctx.Done():
		pix_log.Warning("[WEBSOCKET] ⏰ 广播状态更新超时")
		return
	default:
		s.SendClientStatesUpdate()
	}
}

// 🚀 SendClientStatesUpdate 优化后的发送客户端状态更新
func (s *OptimizedWebSocketService) SendClientStatesUpdate() {
	defer func() {
		if r := recover(); r != nil {
			pix_log.Error("[WEBSOCKET] 💥 发送客户端状态更新异常: %v", r)
		}
	}()

	clientStates := s.GetClientStates()

	response := map[string]interface{}{
		"client_states": clientStates,
		"total_clients": len(clientStates),
		"timestamp":     time.Now().Unix(),
	}

	// 🔧 使用优化后的广播方法
	s.broadcastMessageOptimized("client_states_update", response)

	pix_log.Info("[WEBSOCKET] ✅ 客户端状态更新广播完成，客户端数量: %d", len(clientStates))
}

// 🚀 broadcastMessageOptimized 优化后的消息广播
func (s *OptimizedWebSocketService) broadcastMessageOptimized(messageType string, data interface{}) {
	// 🔧 从对象池获取消息缓冲区
	buf := messagePool.Get().([]byte)
	defer messagePool.Put(buf[:0]) // 重置长度但保留容量

	// 🔧 序列化消息
	message := map[string]interface{}{
		"type": messageType,
		"data": data,
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 💥 消息序列化失败: %v", err)
		return
	}

	// 🔧 更新消息统计
	atomic.AddInt64(&s.metrics.TotalMessages, 1)

	// 🔧 并发广播到所有分片
	var wg sync.WaitGroup
	successCount := int64(0)

	for _, shard := range s.shards {
		wg.Add(1)
		go func(shard *ClientShard) {
			defer wg.Done()

			shard.mutex.RLock()
			defer shard.mutex.RUnlock()

			for conn, clientState := range shard.clients {
				if atomic.LoadInt32(&clientState.IsActive) == 1 {
					// 🔧 使用写入锁防止并发写入
					clientState.WriteMutex.Lock()

					// 🔧 设置写入超时
					conn.SetWriteDeadline(time.Now().Add(s.writeTimeout))

					err := conn.WriteMessage(websocket.TextMessage, jsonData)
					if err != nil {
						pix_log.Warning("[WEBSOCKET] ⚠️ 消息发送失败 SessionID: %s, 错误: %v",
							clientState.SessionID, err)
						// 标记连接为非活跃，等待清理
						atomic.StoreInt32(&clientState.IsActive, 0)
					} else {
						atomic.AddInt64(&successCount, 1)
					}

					clientState.WriteMutex.Unlock()
				}
			}
		}(shard)
	}

	wg.Wait()

	pix_log.Info("[WEBSOCKET] 📡 消息广播完成，类型: %s, 成功: %d", messageType, successCount)
}

// 🚀 兼容性方法和全局服务管理

// 🚀 使用优化版本的WebSocket服务
type WebSocketService = OptimizedWebSocketService

// 全局WebSocket服务实例
var GlobalWebSocketService *WebSocketService

// InitWebSocketService 初始化WebSocket服务
func InitWebSocketService() {
	pix_log.Info("[WEBSOCKET] 🚀 初始化优化版WebSocket服务")
	GlobalWebSocketService = NewOptimizedWebSocketService()
	pix_log.Info("[WEBSOCKET] ✅ WebSocket服务初始化完成")
}

// GetWebSocketService 获取WebSocket服务实例
func GetWebSocketService() *WebSocketService {
	return GlobalWebSocketService
}

// 以下是保留的辅助方法，用于兼容现有代码

// contains 检查字符串是否包含子字符串（不区分大小写）
func contains(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// applyFilters 应用过滤条件
func (s *OptimizedWebSocketService) applyFilters(devices []*tdengine.DeviceStatus, filters map[string]interface{}) []*tdengine.DeviceStatus {
	pix_log.Info("[WEBSOCKET] 开始应用过滤条件，设备总数: %d，过滤条件: %+v", len(devices), filters)

	if len(filters) == 0 {
		pix_log.Info("[WEBSOCKET] 无过滤条件，返回全部设备")
		return devices
	}

	var filtered []*tdengine.DeviceStatus
	offlineThreshold := time.Now().Add(-5 * time.Minute)

	for _, device := range devices {
		include := true

		// 状态过滤
		if statusFilter, exists := filters["status"]; exists && statusFilter != "" && statusFilter != "all" {
			isOnline := device.LastHeartbeatTime.After(offlineThreshold)
			if statusFilter == "online" && !isOnline {
				include = false
			} else if statusFilter == "offline" && isOnline {
				include = false
			}
		}

		// IMSI搜索过滤
		if searchFilter, exists := filters["search"]; exists && searchFilter != "" {
			searchStr := searchFilter.(string)
			if searchStr != "" && !contains(device.IMSI, searchStr) {
				include = false
			}
		}

		if include {
			filtered = append(filtered, device)
		}
	}

	pix_log.Info("[WEBSOCKET] 过滤完成，过滤后设备数: %d", len(filtered))
	return filtered
}

// sortDevices 排序设备列表：在线设备优先，然后按最后心跳时间降序，最后按IMSI字典序
func (s *OptimizedWebSocketService) sortDevices(devices []*tdengine.DeviceStatus) {
	sort.Slice(devices, func(i, j int) bool {
		offlineThreshold := time.Now().Add(-5 * time.Minute)

		iOnline := devices[i].LastHeartbeatTime.After(offlineThreshold)
		jOnline := devices[j].LastHeartbeatTime.After(offlineThreshold)

		// 在线设备优先
		if iOnline != jOnline {
			return iOnline
		}

		// 都在线或都离线时，按最后心跳时间降序
		if !devices[i].LastHeartbeatTime.Equal(devices[j].LastHeartbeatTime) {
			return devices[i].LastHeartbeatTime.After(devices[j].LastHeartbeatTime)
		}

		// 最后按IMSI字典序
		return devices[i].IMSI < devices[j].IMSI
	})
}

// getPagedDevices 获取分页设备数据
func (s *OptimizedWebSocketService) getPagedDevices(devices []*tdengine.DeviceStatus, page, pageSize int, filters map[string]interface{}) []*tdengine.DeviceStatus {
	// 应用过滤条件
	filteredDevices := s.applyFilters(devices, filters)

	// 排序
	s.sortDevices(filteredDevices)

	// 分页
	start := (page - 1) * pageSize
	if start >= len(filteredDevices) {
		return []*tdengine.DeviceStatus{}
	}

	end := start + pageSize
	if end > len(filteredDevices) {
		end = len(filteredDevices)
	}

	return filteredDevices[start:end]
}

// SendDeviceListUpdate 发送设备列表更新
func (s *OptimizedWebSocketService) SendDeviceListUpdate() {
	clientCount := s.GetClientCount()
	if clientCount == 0 {
		return // 没有客户端连接
	}

	pix_log.Info("[WEBSOCKET] 📡 开始发送设备列表更新，客户端数量: %d", clientCount)

	// 🔧 获取所有设备数据
	devices, err := s.tdengineClient.GetAllDevicesStatus()
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备状态失败: %v", err)
		return
	}

	// 🔧 为每个客户端发送对应的数据
	clientStates := s.GetClientStates()
	for _, clientInfo := range clientStates {
		// 根据客户端状态计算分页数据
		pageDevices := s.getPagedDevices(devices, clientInfo.CurrentPage, clientInfo.PageSize, clientInfo.Filters)

		// 计算总数（用于分页）
		filteredDevices := s.applyFilters(devices, clientInfo.Filters)
		totalCount := len(filteredDevices)

		response := map[string]interface{}{
			"devices":     pageDevices,
			"total_count": totalCount,
			"page":        clientInfo.CurrentPage,
			"page_size":   clientInfo.PageSize,
			"timestamp":   time.Now().Unix(),
		}

		// 🔧 广播设备列表更新
		s.BroadcastMessage("device_list_update", response)
	}

	pix_log.Info("[WEBSOCKET] ✅ 设备列表更新完成")
}

// SendDeviceListUpdateToClient 发送设备列表更新给指定客户端
func (s *OptimizedWebSocketService) SendDeviceListUpdateToClient(conn *websocket.Conn) {
	// 🔧 查找客户端状态
	shard := s.getShard(conn)

	shard.mutex.RLock()
	clientState, exists := shard.clients[conn]
	shard.mutex.RUnlock()

	if !exists {
		pix_log.Error("[WEBSOCKET] 客户端连接不存在")
		return
	}

	// 获取设备数据
	devices, err := s.tdengineClient.GetAllDevicesStatus()
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备状态失败: %v", err)
		return
	}

	// 根据客户端状态计算分页数据
	pageDevices := s.getPagedDevices(devices, clientState.CurrentPage, clientState.PageSize, clientState.Filters)

	// 计算总数
	filteredDevices := s.applyFilters(devices, clientState.Filters)
	totalCount := len(filteredDevices)

	response := map[string]interface{}{
		"devices":     pageDevices,
		"total_count": totalCount,
		"page":        clientState.CurrentPage,
		"page_size":   clientState.PageSize,
		"timestamp":   time.Now().Unix(),
	}

	// 发送给指定客户端
	s.SendMessageToClient(conn, "device_list_update", response)

	pix_log.Info("[WEBSOCKET] 设备列表更新发送成功，SessionID: %s，页码: %d",
		clientState.SessionID, clientState.CurrentPage)
}

// HandleMessage 处理WebSocket消息
func (s *OptimizedWebSocketService) HandleMessage(conn *websocket.Conn, messageType int, data []byte) {
	var message map[string]interface{}
	if err := json.Unmarshal(data, &message); err != nil {
		pix_log.Error("[WEBSOCKET] 解析消息失败: %v", err)
		return
	}

	msgType, ok := message["type"].(string)
	if !ok {
		pix_log.Error("[WEBSOCKET] 消息类型无效")
		return
	}

	switch msgType {
	case "get_client_states":
		s.SendClientStatesUpdate()
	case "client_state_update":
		s.handleClientStateUpdate(conn, message)
	case "get_device_list":
		s.SendDeviceListUpdateToClient(conn)
	case "heartbeat":
		s.handleHeartbeat(conn)
	default:
		pix_log.Warning("[WEBSOCKET] 未知消息类型: %s", msgType)
	}
}

// handleClientStateUpdate 处理客户端状态更新
func (s *OptimizedWebSocketService) handleClientStateUpdate(conn *websocket.Conn, message map[string]interface{}) {
	data, ok := message["data"].(map[string]interface{})
	if !ok {
		pix_log.Error("[WEBSOCKET] 客户端状态更新数据无效")
		return
	}

	page := 1
	pageSize := 10
	filters := make(map[string]interface{})

	if p, exists := data["page"]; exists {
		if pf, ok := p.(float64); ok {
			page = int(pf)
		}
	}

	if ps, exists := data["page_size"]; exists {
		if psf, ok := ps.(float64); ok {
			pageSize = int(psf)
		}
	}

	if f, exists := data["filters"]; exists {
		if fm, ok := f.(map[string]interface{}); ok {
			filters = fm
		}
	}

	s.UpdateClientState(conn, page, pageSize, filters)
	s.SendDeviceListUpdateToClient(conn)
}

// handleHeartbeat 处理心跳消息
func (s *OptimizedWebSocketService) handleHeartbeat(conn *websocket.Conn) {
	shard := s.getShard(conn)

	shard.mutex.Lock()
	if clientState, exists := shard.clients[conn]; exists {
		clientState.LastHeartbeat = time.Now()
	}
	shard.mutex.Unlock()
}

// 🚀 BroadcastMessage 优化后的广播消息给所有客户端
func (s *OptimizedWebSocketService) BroadcastMessage(messageType string, data interface{}) {
	// 🔧 使用优化后的广播方法
	s.broadcastMessageOptimized(messageType, data)
}

// 🚀 SendMessageToClient 优化后的发送消息给指定客户端
func (s *OptimizedWebSocketService) SendMessageToClient(conn *websocket.Conn, messageType string, data interface{}) {
	// 🔧 查找客户端状态
	shard := s.getShard(conn)

	shard.mutex.RLock()
	clientState, exists := shard.clients[conn]
	shard.mutex.RUnlock()

	if !exists || atomic.LoadInt32(&clientState.IsActive) != 1 {
		pix_log.Error("[WEBSOCKET] 客户端连接不存在或已非活跃")
		return
	}

	// 🔧 序列化消息
	message := map[string]interface{}{
		"type":      messageType,
		"data":      data,
		"timestamp": time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 序列化消息失败: %v", err)
		return
	}

	// 🔧 安全发送消息
	clientState.WriteMutex.Lock()
	conn.SetWriteDeadline(time.Now().Add(s.writeTimeout))
	err = conn.WriteMessage(websocket.TextMessage, messageBytes)
	clientState.WriteMutex.Unlock()

	if err != nil {
		pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
		atomic.StoreInt32(&clientState.IsActive, 0) // 标记为非活跃
	}
}

// 🚀 UpdateClientState 更新客户端状态
func (s *OptimizedWebSocketService) UpdateClientState(conn *websocket.Conn, page, pageSize int, filters map[string]interface{}) {
	shard := s.getShard(conn)

	shard.mutex.Lock()
	defer shard.mutex.Unlock()

	if clientState, exists := shard.clients[conn]; exists && atomic.LoadInt32(&clientState.IsActive) == 1 {
		clientState.CurrentPage = page
		clientState.PageSize = pageSize
		clientState.LastUpdate = time.Now()

		// 更新过滤条件
		for k := range clientState.Filters {
			delete(clientState.Filters, k)
		}
		for k, v := range filters {
			clientState.Filters[k] = v
		}

		pix_log.Info("[WEBSOCKET] ✅ 客户端状态更新 SessionID: %s, 页码: %d, 每页: %d",
			clientState.SessionID, page, pageSize)
	} else {
		pix_log.Warning("[WEBSOCKET] ⚠️ 要更新的客户端不存在或已非活跃")
	}
}

// 🚀 IsHealthy 健康检查
func (s *OptimizedWebSocketService) IsHealthy() bool {
	if atomic.LoadInt32(&s.isRunning) != 1 {
		return false
	}

	// 🔧 检查连接数是否合理
	clientCount := s.GetClientCount()
	if clientCount < 0 {
		return false
	}

	// 🔧 检查内存使用是否正常
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	if m.Alloc > 8*1024*1024*1024 { // 超过8GB认为异常
		pix_log.Warning("[WEBSOCKET] ⚠️ 内存使用过高: %dMB", m.Alloc/1024/1024)
		return false
	}

	return true
}

// 🚀 Stop 优雅停止服务
func (s *OptimizedWebSocketService) Stop() {
	if !atomic.CompareAndSwapInt32(&s.isRunning, 1, 0) {
		return // 已经停止
	}

	pix_log.Info("[WEBSOCKET] 🛑 开始停止WebSocket服务")

	// 🔧 停止后台任务
	close(s.stopChan)

	if s.cleanupTicker != nil {
		s.cleanupTicker.Stop()
	}
	if s.metricsTicker != nil {
		s.metricsTicker.Stop()
	}

	// 🔧 关闭所有连接
	var wg sync.WaitGroup
	for _, shard := range s.shards {
		wg.Add(1)
		go func(shard *ClientShard) {
			defer wg.Done()

			shard.mutex.Lock()
			defer shard.mutex.Unlock()

			for conn := range shard.clients {
				conn.Close()
			}
		}(shard)
	}

	wg.Wait()
	pix_log.Info("[WEBSOCKET] ✅ WebSocket服务已停止")
}
