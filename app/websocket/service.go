package websocket

import (
	"ccserver/app/service/tdengine"
	"ccserver/app/vars"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/google/uuid"
)

// ClientState 客户端状态
type ClientState struct {
	SessionID     string                 `json:"session_id"`
	CurrentPage   int                    `json:"current_page"`
	PageSize      int                    `json:"page_size"`
	Filters       map[string]interface{} `json:"filters"`
	LastUpdate    time.Time              `json:"last_update"`
	ConnectedTime time.Time              `json:"connected_time"`
	Connection    *websocket.Conn        `json:"-"`
}

// ClientStateInfo 客户端状态信息（用于前端显示）
type ClientStateInfo struct {
	SessionID     string                 `json:"session_id"`
	CurrentPage   int                    `json:"current_page"`
	PageSize      int                    `json:"page_size"`
	Filters       map[string]interface{} `json:"filters"`
	LastUpdate    time.Time              `json:"last_update"`
	ConnectedTime time.Time              `json:"connected_time"`
	Duration      string                 `json:"duration"`
}

// WebSocketService WebSocket服务
type WebSocketService struct {
	clients        map[*websocket.Conn]*ClientState
	clientsMutex   sync.RWMutex
	connMutex      sync.Mutex // 专门用于连接建立和断开操作的锁
	tdengineClient *tdengine.TDengineClient
	stopChan       chan bool
	isRunning      bool
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService() *WebSocketService {
	pix_log.Info("[WEBSOCKET] 创建WebSocket服务实例")

	// 创建TDengine客户端
	client := tdengine.NewTDengineClient()
	pix_log.Info("[WEBSOCKET] TDengine客户端创建成功")

	service := &WebSocketService{
		clients:        make(map[*websocket.Conn]*ClientState),
		tdengineClient: client,
		stopChan:       make(chan bool),
		isRunning:      false,
	}

	pix_log.Info("[WEBSOCKET] WebSocket服务实例创建完成")
	return service
}

// AddClient 添加客户端连接
func (s *WebSocketService) AddClient(conn *websocket.Conn) {
	fmt.Println("========== AddClient 开始 ==========")
	fmt.Printf("========== 连接地址: %s ==========\n", conn.RemoteAddr().String())

	// 使用连接锁确保连接操作的原子性
	s.connMutex.Lock()
	defer s.connMutex.Unlock()

	// 在锁内完成客户端添加操作
	func() {
		s.clientsMutex.Lock()
		defer s.clientsMutex.Unlock()

		// 获取客户端IP地址（去除端口号）
		clientIP := s.getClientIP(conn)
		fmt.Printf("========== 客户端IP: %s ==========\n", clientIP)

		// 检查是否已经存在这个连接
		if existingState, exists := s.clients[conn]; exists {
			fmt.Printf("========== 警告：连接已存在！SessionID: %s ==========\n", existingState.SessionID)
		}

		// 创建新的客户端状态
		sessionID := uuid.New().String()
		now := time.Now()
		clientState := &ClientState{
			SessionID:     sessionID,
			CurrentPage:   1,
			PageSize:      10,
			Filters:       make(map[string]interface{}),
			LastUpdate:    now,
			ConnectedTime: now,
			Connection:    conn,
		}

		s.clients[conn] = clientState
		fmt.Printf("========== 新客户端连接，SessionID: %s，连接地址: %s ==========\n", sessionID, conn.RemoteAddr().String())
		fmt.Printf("========== 当前连接数: %d ==========\n", len(s.clients))
		fmt.Printf("========== 所有连接列表: ==========\n")
		for c, state := range s.clients {
			fmt.Printf("  - 连接: %s, SessionID: %s, 连接时间: %s\n", c.RemoteAddr().String(), state.SessionID, state.ConnectedTime.Format("15:04:05"))
		}
		fmt.Printf("========== 客户端状态: %+v ==========\n", clientState)
		fmt.Println("========== AddClient 完成 ==========")
	}()

	// 🔧 修复：在释放锁后广播客户端状态更新，避免死锁
	fmt.Printf("========== 新客户端连接后广播状态更新 ==========\n")
	s.SendClientStatesUpdate()
	fmt.Printf("========== 客户端状态更新广播完成 ==========\n")
}

// RemoveClient 移除客户端连接
func (s *WebSocketService) RemoveClient(conn *websocket.Conn) {
	fmt.Printf("========== RemoveClient 开始 ==========\n")
	fmt.Printf("========== 要移除的连接地址: %s ==========\n", conn.RemoteAddr().String())

	// 使用连接锁确保断开操作的原子性
	s.connMutex.Lock()
	defer s.connMutex.Unlock()

	// 在锁内完成客户端移除操作
	func() {
		s.clientsMutex.Lock()
		defer s.clientsMutex.Unlock()

		fmt.Printf("========== 移除前连接数: %d ==========\n", len(s.clients))
		fmt.Printf("========== 移除前所有连接列表: ==========\n")
		for c, state := range s.clients {
			fmt.Printf("  - 连接: %s, SessionID: %s, 连接时间: %s\n", c.RemoteAddr().String(), state.SessionID, state.ConnectedTime.Format("15:04:05"))
		}

		if clientState, exists := s.clients[conn]; exists {
			fmt.Printf("========== 找到要移除的客户端，SessionID: %s ==========\n", clientState.SessionID)
			pix_log.Info("[WEBSOCKET] 客户端断开连接，SessionID: %s，当前连接数: %d", clientState.SessionID, len(s.clients)-1)
			delete(s.clients, conn)
			conn.Close()
			fmt.Printf("========== 客户端已移除，移除后连接数: %d ==========\n", len(s.clients))
		} else {
			fmt.Printf("========== 警告：要移除的连接不存在！连接地址: %s ==========\n", conn.RemoteAddr().String())
		}

		fmt.Printf("========== 移除后所有连接列表: ==========\n")
		for c, state := range s.clients {
			fmt.Printf("  - 连接: %s, SessionID: %s, 连接时间: %s\n", c.RemoteAddr().String(), state.SessionID, state.ConnectedTime.Format("15:04:05"))
		}
		fmt.Printf("========== RemoveClient 完成 ==========\n")
	}()

	// 🔧 修复：在释放锁后立即广播客户端状态更新，确保时序正确
	fmt.Printf("========== 客户端断开后广播状态更新 ==========\n")
	s.SendClientStatesUpdate()
	fmt.Printf("========== 客户端状态更新广播完成 ==========\n")
}

// getClientIP 从连接中提取客户端IP地址（去除端口号）
func (s *WebSocketService) getClientIP(conn *websocket.Conn) string {
	addr := conn.RemoteAddr().String()
	// 对于IPv6地址，格式可能是 [::1]:port
	// 对于IPv4地址，格式是 ip:port
	if strings.HasPrefix(addr, "[") {
		// IPv6地址
		if idx := strings.LastIndex(addr, "]:"); idx != -1 {
			return addr[1:idx] // 去掉开头的 [ 和结尾的 ]:port
		}
	} else {
		// IPv4地址
		if idx := strings.LastIndex(addr, ":"); idx != -1 {
			return addr[:idx] // 去掉 :port
		}
	}
	return addr // 如果解析失败，返回原始地址
}

// GetClientCount 获取客户端连接数
func (s *WebSocketService) GetClientCount() int {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	count := len(s.clients)
	fmt.Printf("========== GetClientCount: %d ==========\n", count)
	fmt.Printf("========== 时间戳: %s ==========\n", time.Now().Format("2006-01-02 15:04:05.000"))

	if count > 0 {
		fmt.Printf("========== 当前活跃连接列表: ==========\n")
		for conn, state := range s.clients {
			fmt.Printf("  📱 %s | SessionID: %s | 活跃时长: %v\n",
				conn.RemoteAddr().String(),
				state.SessionID,
				time.Since(state.ConnectedTime).Round(time.Second))
		}
	}

	return count
}

// SendMessageToClient 发送消息给指定客户端
func (s *WebSocketService) SendMessageToClient(conn *websocket.Conn, messageType string, data interface{}) {
	message := map[string]interface{}{
		"type":      messageType,
		"data":      data,
		"timestamp": time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 序列化消息失败: %v", err)
		return
	}

	err = conn.WriteMessage(websocket.TextMessage, messageBytes)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
		s.RemoveClient(conn)
	}
}

// BroadcastMessage 广播消息给所有客户端
func (s *WebSocketService) BroadcastMessage(messageType string, data interface{}) {
	fmt.Printf("========== BroadcastMessage 开始，消息类型: %s ==========\n", messageType)
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	fmt.Printf("========== 当前客户端连接数: %d ==========\n", len(s.clients))

	message := map[string]interface{}{
		"type":      messageType,
		"data":      data,
		"timestamp": time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		fmt.Printf("========== 序列化消息失败: %v ==========\n", err)
		pix_log.Error("[WEBSOCKET] 序列化消息失败: %v", err)
		return
	}

	fmt.Printf("========== 消息序列化成功，长度: %d 字节 ==========\n", len(messageBytes))

	// 记录需要移除的连接
	var toRemove []*websocket.Conn
	successCount := 0

	for conn := range s.clients {
		fmt.Printf("========== 向客户端发送消息... ==========\n")
		err := conn.WriteMessage(websocket.TextMessage, messageBytes)
		if err != nil {
			fmt.Printf("========== 发送消息失败: %v ==========\n", err)
			pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
			toRemove = append(toRemove, conn)
		} else {
			fmt.Printf("========== 消息发送成功 ==========\n")
			successCount++
		}
	}

	// 移除失效的连接
	for _, conn := range toRemove {
		s.RemoveClient(conn)
	}

	fmt.Printf("========== 广播完成，成功: %d，失败: %d ==========\n", successCount, len(toRemove))

	if len(toRemove) == 0 {
		pix_log.Info("[WEBSOCKET] 广播消息成功，类型: %s，接收者: %d", messageType, len(s.clients))
	}
	fmt.Printf("========== BroadcastMessage 完成 ==========\n")
}

// SendDeviceListUpdate 发送设备列表更新（优化版本）
func (s *WebSocketService) SendDeviceListUpdate() {
	fmt.Println("========== 开始定时任务：发送设备列表更新 ==========")

	// 获取客户端锁来分析过滤条件
	s.clientsMutex.RLock()
	if len(s.clients) == 0 {
		s.clientsMutex.RUnlock()
		fmt.Println("没有客户端连接，跳过更新")
		return
	}

	fmt.Printf("当前连接的客户端数量: %d\n", len(s.clients))

	// 打印所有客户端的状态
	for conn, clientState := range s.clients {
		fmt.Printf("客户端 %s - 页码: %d, 每页: %d, 过滤条件: %+v\n",
			clientState.SessionID, clientState.CurrentPage, clientState.PageSize, clientState.Filters)
		_ = conn // 避免未使用变量警告
	}

	// 检查是否所有客户端都有相同的过滤条件
	if commonFilters, allSame := s.getCommonFilters(); allSame {
		s.clientsMutex.RUnlock()
		fmt.Printf("所有客户端过滤条件相同: %+v，使用优化查询\n", commonFilters)
		pix_log.Info("[WEBSOCKET] 所有客户端过滤条件相同，使用优化查询")
		s.sendDeviceListUpdateOptimized(commonFilters)
	} else {
		s.clientsMutex.RUnlock()
		fmt.Println("客户端过滤条件不同，使用分组查询")
		pix_log.Info("[WEBSOCKET] 客户端过滤条件不同，使用分组查询")
		s.sendDeviceListUpdateGrouped()
	}

	fmt.Println("========== 定时任务完成 ==========")
}

// getCommonFilters 检查所有客户端是否有相同的过滤条件
func (s *WebSocketService) getCommonFilters() (map[string]interface{}, bool) {
	fmt.Printf("========== 检查客户端过滤条件是否相同 ==========\n")

	if len(s.clients) == 0 {
		fmt.Println("没有客户端连接")
		return nil, false
	}

	// 获取第一个客户端的过滤条件作为基准
	var baseFilters map[string]interface{}
	var baseSessionID string
	for _, clientState := range s.clients {
		baseFilters = clientState.Filters
		baseSessionID = clientState.SessionID
		break
	}

	fmt.Printf("基准客户端 %s 的过滤条件: %+v\n", baseSessionID, baseFilters)

	// 检查是否所有客户端都有相同的过滤条件
	allSame := true
	for _, clientState := range s.clients {
		fmt.Printf("比较客户端 %s 的过滤条件: %+v\n", clientState.SessionID, clientState.Filters)
		if !s.filtersEqual(baseFilters, clientState.Filters) {
			fmt.Printf("客户端 %s 的过滤条件与基准不同\n", clientState.SessionID)
			allSame = false
			break
		}
	}

	if allSame {
		fmt.Printf("所有 %d 个客户端具有相同过滤条件: %+v\n", len(s.clients), baseFilters)
		pix_log.Info("[WEBSOCKET] 所有 %d 个客户端具有相同过滤条件: %+v", len(s.clients), baseFilters)
		return baseFilters, true
	} else {
		fmt.Println("客户端过滤条件不同")
		return nil, false
	}
}

// sendDeviceListUpdateOptimized 当所有客户端过滤条件相同时的优化处理
func (s *WebSocketService) sendDeviceListUpdateOptimized(commonFilters map[string]interface{}) {
	// 直接在数据库层面应用过滤条件
	devices, err := s.queryDevicesWithFilters(commonFilters)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 优化查询失败，回退到原始方法: %v", err)
		s.sendDeviceListUpdateOriginal()
		return
	}

	// 排序设备
	s.sortDevices(devices)

	// 计算统计信息
	stats := s.calculateStats(devices)
	totalDevices := len(devices)

	pix_log.Info("[WEBSOCKET] 优化查询完成，设备数: %d", totalDevices)

	// 获取客户端锁进行分发
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	// 为每个客户端发送对应页面的数据
	var toRemove []*websocket.Conn
	for conn, clientState := range s.clients {
		// 由于过滤条件相同，只需要分页
		pageDevices := s.getPagedDevicesSimple(devices, clientState.CurrentPage, clientState.PageSize)

		response := map[string]interface{}{
			"devices":     pageDevices,
			"stats":       stats,
			"total":       totalDevices,
			"current_page": clientState.CurrentPage,
			"page_size":   clientState.PageSize,
			"session_id":  clientState.SessionID,
		}

		err := conn.WriteJSON(map[string]interface{}{
			"type":      "device_list_update",
			"data":      response,
			"timestamp": time.Now().Unix(),
		})

		if err != nil {
			pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
			toRemove = append(toRemove, conn)
		}
	}

	// 清理断开的连接
	s.cleanupConnections(toRemove)
}

// sendDeviceListUpdateGrouped 使用分组查询处理不同过滤条件的客户端
func (s *WebSocketService) sendDeviceListUpdateGrouped() {
	// 获取客户端分组
	s.clientsMutex.RLock()
	groups := s.groupClientsByFilters()
	s.clientsMutex.RUnlock()

	if len(groups) == 0 {
		return
	}

	pix_log.Info("[WEBSOCKET] 分组查询：共 %d 个不同的过滤条件组", len(groups))

	// 并发查询不同的过滤条件
	var wg sync.WaitGroup
	results := make(map[string]*QueryResult)
	resultsMutex := sync.Mutex{}

	for filterKey, group := range groups {
		wg.Add(1)
		go func(key string, g *QueryGroup) {
			defer wg.Done()

			// 查询这个组的数据
			devices, err := s.queryDevicesWithFilters(g.Filters)
			if err != nil {
				pix_log.Error("[WEBSOCKET] 分组查询失败 [%s]: %v", key, err)
				resultsMutex.Lock()
				results[key] = &QueryResult{Error: err}
				resultsMutex.Unlock()
				return
			}

			// 排序和统计
			s.sortDevices(devices)
			stats := s.calculateStats(devices)

			resultsMutex.Lock()
			results[key] = &QueryResult{
				Devices: devices,
				Stats:   stats,
				Total:   len(devices),
			}
			resultsMutex.Unlock()

			pix_log.Info("[WEBSOCKET] 分组查询完成 [%s]：设备数 %d", key, len(devices))
		}(filterKey, group)
	}

	wg.Wait()

	// 分发结果给各个客户端
	s.distributeGroupedResults(groups, results)
}

// sendDeviceListUpdateOriginal 原始的查询方法（作为回退方案）
func (s *WebSocketService) sendDeviceListUpdateOriginal() {
	// 获取所有设备数据
	allDevices, err := s.tdengineClient.GetAllDevicesStatus()
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备列表失败: %v", err)
		return
	}

	// 排序设备
	s.sortDevices(allDevices)

	// 计算统计信息
	stats := s.calculateStats(allDevices)
	totalDevices := len(allDevices)

	// 获取客户端锁进行操作
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	// 为每个客户端发送对应页面的数据
	var toRemove []*websocket.Conn
	for conn, clientState := range s.clients {
		// 根据客户端状态计算分页数据
		pageDevices := s.getPagedDevices(allDevices, clientState.CurrentPage, clientState.PageSize, clientState.Filters)

		response := map[string]interface{}{
			"devices":     pageDevices,
			"stats":       stats,
			"total":       totalDevices,
			"current_page": clientState.CurrentPage,
			"page_size":   clientState.PageSize,
			"session_id":  clientState.SessionID,
		}

		err := conn.WriteJSON(map[string]interface{}{
			"type":      "device_list_update",
			"data":      response,
			"timestamp": time.Now().Unix(),
		})

		if err != nil {
			pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
			toRemove = append(toRemove, conn)
		}
	}

	// 移除失效的连接
	for _, conn := range toRemove {
		s.RemoveClient(conn)
	}

	if len(toRemove) == 0 && len(s.clients) > 0 {
		pix_log.Info("[WEBSOCKET] 设备列表更新推送完成，接收者: %d", len(s.clients))
	}
}

// QueryGroup 表示具有相同查询条件的客户端组
type QueryGroup struct {
	FilterKey string                    // 过滤条件的唯一标识
	Filters   map[string]interface{}    // 过滤条件
	Clients   []*ClientState           // 具有相同过滤条件的客户端列表
}

// QueryResult 表示查询结果
type QueryResult struct {
	Devices []*tdengine.DeviceStatus
	Stats   map[string]interface{}
	Total   int
	Error   error
}

// filtersEqual 比较两个过滤条件是否相等
func (s *WebSocketService) filtersEqual(filters1, filters2 map[string]interface{}) bool {
	if len(filters1) != len(filters2) {
		return false
	}

	for key, value1 := range filters1 {
		value2, exists := filters2[key]
		if !exists || value1 != value2 {
			return false
		}
	}

	return true
}

// getFilterKey 生成过滤条件的唯一标识
func (s *WebSocketService) getFilterKey(filters map[string]interface{}) string {
	search := ""
	status := ""
	workMode := ""

	if v, ok := filters["search"]; ok && v != nil && v != "" {
		if str, ok := v.(string); ok {
			search = str
		} else {
			search = fmt.Sprintf("%v", v)
		}
	}
	if v, ok := filters["status"]; ok && v != nil && v != "" {
		if str, ok := v.(string); ok {
			status = str
		} else {
			status = fmt.Sprintf("%v", v)
		}
	}
	if v, ok := filters["workMode"]; ok && v != nil && v != "" {
		if str, ok := v.(string); ok {
			workMode = str
		} else {
			workMode = fmt.Sprintf("%v", v)
		}
	}

	return fmt.Sprintf("search:%s|status:%s|workMode:%s", search, status, workMode)
}

// groupClientsByFilters 将客户端按过滤条件分组
func (s *WebSocketService) groupClientsByFilters() map[string]*QueryGroup {
	groups := make(map[string]*QueryGroup)

	for _, clientState := range s.clients {
		filterKey := s.getFilterKey(clientState.Filters)

		if group, exists := groups[filterKey]; exists {
			group.Clients = append(group.Clients, clientState)
		} else {
			groups[filterKey] = &QueryGroup{
				FilterKey: filterKey,
				Filters:   clientState.Filters,
				Clients:   []*ClientState{clientState},
			}
		}
	}

	return groups
}

// queryDevicesWithFilters 根据过滤条件查询设备数据
func (s *WebSocketService) queryDevicesWithFilters(filters map[string]interface{}) ([]*tdengine.DeviceStatus, error) {
	fmt.Printf("========== queryDevicesWithFilters 开始 ==========\n")
	fmt.Printf("接收到的过滤条件: %+v\n", filters)

	// 如果没有过滤条件，查询全量数据
	if len(filters) == 0 {
		fmt.Println("没有过滤条件，查询全量数据")
		return s.tdengineClient.GetAllDevicesStatus()
	}

	// 检查是否有搜索条件
	if search, ok := filters["search"]; ok {
		fmt.Printf("找到search字段，值: %v，类型: %T\n", search, search)

		if search != nil && search != "" {
			// 安全的类型转换
			var searchStr string
			switch v := search.(type) {
			case string:
				searchStr = v
			case interface{}:
				searchStr = fmt.Sprintf("%v", v)
			}

			if searchStr != "" {
				fmt.Printf("使用搜索条件查询: '%s'\n", searchStr)
				pix_log.Info("[WEBSOCKET] 使用搜索条件查询: %s", searchStr)
				result, err := s.tdengineClient.SearchDevicesByIMSI(searchStr)
				if err != nil {
					fmt.Printf("搜索查询失败: %v\n", err)
				} else {
					fmt.Printf("搜索查询成功，返回 %d 个设备\n", len(result))
				}
				return result, err
			}
		}
	} else {
		fmt.Println("没有找到search字段")
	}

	// 没有搜索条件但有其他过滤条件，仍然查询全量然后过滤
	fmt.Println("没有有效搜索条件，查询全量数据")
	// TODO: 未来可以在数据库层面实现状态过滤
	result, err := s.tdengineClient.GetAllDevicesStatus()
	if err != nil {
		fmt.Printf("全量查询失败: %v\n", err)
	} else {
		fmt.Printf("全量查询成功，返回 %d 个设备\n", len(result))
	}
	fmt.Printf("========== queryDevicesWithFilters 结束 ==========\n")
	return result, err
}

// getPagedDevicesSimple 简单分页（不需要过滤，因为数据已经过滤过了）
func (s *WebSocketService) getPagedDevicesSimple(devices []*tdengine.DeviceStatus, page, pageSize int) []*tdengine.DeviceStatus {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= len(devices) {
		return []*tdengine.DeviceStatus{}
	}

	if end > len(devices) {
		end = len(devices)
	}

	return devices[start:end]
}

// distributeGroupedResults 分发分组查询结果给各个客户端
func (s *WebSocketService) distributeGroupedResults(groups map[string]*QueryGroup, results map[string]*QueryResult) {
	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	var toRemove []*websocket.Conn

	for filterKey, group := range groups {
		result, exists := results[filterKey]
		if !exists || result.Error != nil {
			pix_log.Error("[WEBSOCKET] 分组 [%s] 查询失败，跳过", filterKey)
			continue
		}

		// 为这个组的所有客户端发送数据
		for _, clientState := range group.Clients {
			conn := clientState.Connection
			if conn == nil {
				continue
			}

			// 分页处理
			pageDevices := s.getPagedDevicesSimple(result.Devices, clientState.CurrentPage, clientState.PageSize)

			response := map[string]interface{}{
				"devices":     pageDevices,
				"stats":       result.Stats,
				"total":       result.Total,
				"current_page": clientState.CurrentPage,
				"page_size":   clientState.PageSize,
				"session_id":  clientState.SessionID,
			}

			err := conn.WriteJSON(map[string]interface{}{
				"type":      "device_list_update",
				"data":      response,
				"timestamp": time.Now().Unix(),
			})

			if err != nil {
				pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
				toRemove = append(toRemove, conn)
			}
		}
	}

	// 清理断开的连接
	s.cleanupConnections(toRemove)
}

// cleanupConnections 清理断开的连接
func (s *WebSocketService) cleanupConnections(toRemove []*websocket.Conn) {
	for _, conn := range toRemove {
		s.RemoveClient(conn)
	}
}

// SendDeviceDetailUpdate 发送设备详情更新
func (s *WebSocketService) SendDeviceDetailUpdate(imsi string) {
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)
	device, err := s.tdengineClient.GetLatestDeviceStatus(tableName)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备详情失败: %v", err)
		return
	}

	s.BroadcastMessage("device_detail_update", map[string]interface{}{
		"imsi":   imsi,
		"device": device,
	})
}

// SendDeviceHistoryUpdate 发送设备历史更新
func (s *WebSocketService) SendDeviceHistoryUpdate(imsi string, limit int) {
	// 获取最近24小时的历史数据
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)
	
	history, err := s.tdengineClient.GetDeviceHistory(imsi, startTime, endTime, limit)
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备历史失败: %v", err)
		return
	}

	s.BroadcastMessage("device_history_update", map[string]interface{}{
		"imsi":    imsi,
		"history": history,
		"total":   len(history),
	})
}

// calculateStats 计算设备统计信息
func (s *WebSocketService) calculateStats(devices []*tdengine.DeviceStatus) map[string]interface{} {
	totalDevices := len(devices)
	onlineDevices := 0
	offlineDevices := 0
	errorDevices := 0

	// 离线阈值（5分钟）
	offlineThreshold := time.Now().Add(-5 * time.Minute)

	for _, device := range devices {
		// 判断设备状态 - device.LastHeartbeatTime 已经是 time.Time 类型
		if device.LastHeartbeatTime.After(offlineThreshold) {
			onlineDevices++
		} else {
			offlineDevices++
		}

		// 检查错误状态（这里可以根据具体业务逻辑调整）
		if device.ErrorCount > 0 {
			errorDevices++
		}
	}

	onlineRate := 0.0
	offlineRate := 0.0
	if totalDevices > 0 {
		onlineRate = float64(onlineDevices) / float64(totalDevices) * 100
		offlineRate = float64(offlineDevices) / float64(totalDevices) * 100
	}

	return map[string]interface{}{
		"total_devices":   totalDevices,
		"online_devices":  onlineDevices,
		"offline_devices": offlineDevices,
		"error_devices":   errorDevices,
		"online_rate":     onlineRate,
		"offline_rate":    offlineRate,
	}
}

// getPagedDevices 获取分页设备数据
func (s *WebSocketService) getPagedDevices(allDevices []*tdengine.DeviceStatus, page, pageSize int, filters map[string]interface{}) []*tdengine.DeviceStatus {
	// 应用过滤条件
	filteredDevices := s.applyFilters(allDevices, filters)

	// 计算分页
	totalDevices := len(filteredDevices)
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= totalDevices {
		return []*tdengine.DeviceStatus{}
	}

	if endIndex > totalDevices {
		endIndex = totalDevices
	}

	return filteredDevices[startIndex:endIndex]
}

// applyFilters 应用过滤条件
func (s *WebSocketService) applyFilters(devices []*tdengine.DeviceStatus, filters map[string]interface{}) []*tdengine.DeviceStatus {
	pix_log.Info("[WEBSOCKET] 开始应用过滤条件，设备总数: %d，过滤条件: %+v", len(devices), filters)

	if len(filters) == 0 {
		pix_log.Info("[WEBSOCKET] 无过滤条件，返回全部设备")
		return devices
	}

	var filtered []*tdengine.DeviceStatus
	offlineThreshold := time.Now().Add(-5 * time.Minute)

	for _, device := range devices {
		include := true

		// 状态过滤
		if statusFilter, exists := filters["status"]; exists && statusFilter != "" && statusFilter != "all" {
			isOnline := device.LastHeartbeatTime.After(offlineThreshold)
			if statusFilter == "online" && !isOnline {
				include = false
				pix_log.Info("[WEBSOCKET] 设备 %s 被状态过滤排除（要求在线但设备离线）", device.IMSI)
			} else if statusFilter == "offline" && isOnline {
				include = false
				pix_log.Info("[WEBSOCKET] 设备 %s 被状态过滤排除（要求离线但设备在线）", device.IMSI)
			}
		}

		// IMSI搜索过滤
		if searchFilter, exists := filters["search"]; exists && searchFilter != "" {
			searchStr := searchFilter.(string)
			if searchStr != "" && !contains(device.IMSI, searchStr) {
				include = false
				pix_log.Info("[WEBSOCKET] 设备 %s 被搜索过滤排除（搜索条件: %s）", device.IMSI, searchStr)
			}
		}

		if include {
			filtered = append(filtered, device)
			pix_log.Info("[WEBSOCKET] 设备 %s 通过过滤条件", device.IMSI)
		}
	}

	pix_log.Info("[WEBSOCKET] 过滤完成，过滤后设备数: %d", len(filtered))
	return filtered
}

// sortDevices 排序设备列表：在线设备优先，然后按最后心跳时间降序，最后按IMSI字典序
func (s *WebSocketService) sortDevices(devices []*tdengine.DeviceStatus) {
	sort.Slice(devices, func(i, j int) bool {
		deviceA := devices[i]
		deviceB := devices[j]

		// 首先按状态排序：在线(1)优先于离线(0)
		if deviceA.CurrentStatus != deviceB.CurrentStatus {
			return deviceA.CurrentStatus > deviceB.CurrentStatus
		}

		// 状态相同时，按最后心跳时间降序排序（较晚的排在前面）
		if !deviceA.LastHeartbeatTime.Equal(deviceB.LastHeartbeatTime) {
			return deviceA.LastHeartbeatTime.After(deviceB.LastHeartbeatTime)
		}

		// 状态和心跳时间都相同时，按IMSI字典序排序（自然排序）
		return deviceA.IMSI < deviceB.IMSI
	})

	pix_log.Info("[WEBSOCKET] 设备列表已排序: 在线设备优先，按心跳时间降序，按IMSI字典序")
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(str, substr string) bool {
	if substr == "" {
		return true
	}

	// 转换为小写进行比较
	lowerStr := strings.ToLower(str)
	lowerSubstr := strings.ToLower(substr)

	// 使用Go标准库的Contains函数
	result := strings.Contains(lowerStr, lowerSubstr)

	// 添加调试日志
	pix_log.Info("[WEBSOCKET] 搜索过滤: '%s' 包含 '%s' = %v", str, substr, result)

	return result
}

// UpdateClientState 更新客户端状态
func (s *WebSocketService) UpdateClientState(conn *websocket.Conn, page, pageSize int, filters map[string]interface{}) {
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	if clientState, exists := s.clients[conn]; exists {
		fmt.Printf("========== 更新客户端状态 ==========\n")
		fmt.Printf("SessionID: %s\n", clientState.SessionID)
		fmt.Printf("旧状态 - 页码: %d, 每页: %d, 过滤条件: %+v\n",
			clientState.CurrentPage, clientState.PageSize, clientState.Filters)

		clientState.CurrentPage = page
		clientState.PageSize = pageSize
		clientState.Filters = filters
		clientState.LastUpdate = time.Now()

		fmt.Printf("新状态 - 页码: %d, 每页: %d, 过滤条件: %+v\n",
			clientState.CurrentPage, clientState.PageSize, clientState.Filters)

		// 检查搜索条件
		if search, ok := filters["search"].(string); ok && search != "" {
			fmt.Printf("检测到搜索条件: '%s'\n", search)
		} else {
			fmt.Println("没有搜索条件或搜索条件为空")
		}

		pix_log.Info("[WEBSOCKET] 客户端状态更新，SessionID: %s，页码: %d，每页: %d，过滤条件: %+v",
			clientState.SessionID, page, pageSize, filters)
		fmt.Printf("========== 客户端状态更新完成 ==========\n")
	} else {
		fmt.Println("客户端连接不存在，无法更新状态")
	}
}

// Start 启动WebSocket服务
func (s *WebSocketService) Start() {
	if s.isRunning {
		pix_log.Info("[WEBSOCKET] 服务已经在运行中")
		return
	}

	s.isRunning = true
	pix_log.Info("[WEBSOCKET] 启动WebSocket服务")

	// 启动定时推送
	go s.startPeriodicUpdates()
}

// Stop 停止WebSocket服务
func (s *WebSocketService) Stop() {
	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.stopChan)

	// 关闭所有客户端连接
	s.clientsMutex.Lock()
	defer s.clientsMutex.Unlock()

	for conn := range s.clients {
		conn.Close()
	}
	s.clients = make(map[*websocket.Conn]*ClientState)

	pix_log.Info("[WEBSOCKET] WebSocket服务已停止")
}

// startPeriodicUpdates 启动定时更新
func (s *WebSocketService) startPeriodicUpdates() {
	// 从配置中获取推送间隔，默认30秒
	interval := vars.Config.Business.PushInterval
	if interval == 0 {
		interval = 30 * time.Second
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	pix_log.Info("[WEBSOCKET] 启动定时推送，间隔: %v", interval)

	for {
		select {
		case <-ticker.C:
			if s.GetClientCount() > 0 {
				s.SendDeviceListUpdate()
			}
		case <-s.stopChan:
			pix_log.Info("[WEBSOCKET] 停止定时推送")
			return
		}
	}
}

// HandleMessage 处理客户端消息
func (s *WebSocketService) HandleMessage(conn *websocket.Conn, messageType int, data []byte) {
	fmt.Println("========== HandleMessage 开始处理消息 ==========")
	fmt.Printf("========== 消息类型: %d，数据: %s ==========\n", messageType, string(data))

	var message map[string]interface{}
	if err := json.Unmarshal(data, &message); err != nil {
		fmt.Printf("========== 解析客户端消息失败: %v ==========\n", err)
		return
	}

	msgType, ok := message["type"].(string)
	if !ok {
		fmt.Printf("========== 消息类型无效，消息内容: %+v ==========\n", message)
		return
	}

	fmt.Printf("========== 解析成功，消息类型: %s ==========\n", msgType)

	switch msgType {
	case "get_device_list":
		fmt.Println("========== 处理 get_device_list 消息 ==========")
		s.SendDeviceListUpdate()
		fmt.Println("========== get_device_list 消息处理完成 ==========")
	case "get_client_states":
		fmt.Println("========== 处理 get_client_states 消息 ==========")
		fmt.Printf("========== 请求来源: %s ==========\n", conn.RemoteAddr().String())
		fmt.Printf("========== 当前连接数: %d ==========\n", s.GetClientCount())
		// 发送客户端状态信息
		s.SendClientStatesUpdate()
		fmt.Println("========== get_client_states 消息处理完成 ==========")
	case "client_state_update":
		// 更新客户端状态
		fmt.Println("========== 收到 client_state_update 消息 ==========")
		fmt.Printf("原始消息数据: %+v\n", message)

		if data, ok := message["data"].(map[string]interface{}); ok {
			fmt.Printf("解析后的data: %+v\n", data)

			page := 1
			pageSize := 10
			filters := make(map[string]interface{})

			if p, ok := data["page"].(float64); ok {
				page = int(p)
				fmt.Printf("解析到页码: %d\n", page)
			}
			if ps, ok := data["page_size"].(float64); ok {
				pageSize = int(ps)
				fmt.Printf("解析到每页大小: %d\n", pageSize)
			}
			if f, ok := data["filters"].(map[string]interface{}); ok {
				filters = f
				fmt.Printf("解析到过滤条件: %+v\n", filters)
			} else {
				fmt.Println("没有找到filters字段或类型不匹配")
			}

			fmt.Printf("最终参数 - 页码: %d, 每页: %d, 过滤条件: %+v\n", page, pageSize, filters)
			s.UpdateClientState(conn, page, pageSize, filters)

			// 立即发送更新后的数据
			s.SendDeviceListUpdateToClient(conn)

			// 广播客户端状态更新
			s.SendClientStatesUpdate()
		} else {
			fmt.Println("无法解析client_state_update消息的data字段")
		}
		fmt.Println("========== client_state_update 消息处理完成 ==========")
	case "get_device_detail":
		if imsi, ok := message["imsi"].(string); ok {
			s.SendDeviceDetailUpdate(imsi)
		}
	case "get_device_history":
		if imsi, ok := message["imsi"].(string); ok {
			limit := 100 // 默认限制
			if l, ok := message["limit"].(float64); ok {
				limit = int(l)
			}
			s.SendDeviceHistoryUpdate(imsi, limit)
		}
	case "ping":
		// 响应ping消息
		response := map[string]interface{}{
			"type":      "pong",
			"timestamp": time.Now().Unix(),
		}
		responseBytes, _ := json.Marshal(response)
		conn.WriteMessage(websocket.TextMessage, responseBytes)
	default:
		pix_log.Info("[WEBSOCKET] 未知消息类型: %s", msgType)
	}
}

// SendDeviceListUpdateToClient 发送设备列表更新给指定客户端
func (s *WebSocketService) SendDeviceListUpdateToClient(conn *websocket.Conn) {
	s.clientsMutex.RLock()
	clientState, exists := s.clients[conn]
	s.clientsMutex.RUnlock()

	if !exists {
		return
	}

	// 获取所有设备数据
	allDevices, err := s.tdengineClient.GetAllDevicesStatus()
	if err != nil {
		pix_log.Error("[WEBSOCKET] 获取设备列表失败: %v", err)
		return
	}

	// 排序设备：在线设备优先，然后按最后心跳时间降序
	s.sortDevices(allDevices)

	// 计算统计信息
	stats := s.calculateStats(allDevices)
	totalDevices := len(allDevices)

	// 根据客户端状态计算分页数据
	pageDevices := s.getPagedDevices(allDevices, clientState.CurrentPage, clientState.PageSize, clientState.Filters)

	response := map[string]interface{}{
		"devices":     pageDevices,
		"stats":       stats,
		"total":       totalDevices,
		"current_page": clientState.CurrentPage,
		"page_size":   clientState.PageSize,
		"session_id":  clientState.SessionID,
	}

	err = conn.WriteJSON(map[string]interface{}{
		"type":      "device_list_update",
		"data":      response,
		"timestamp": time.Now().Unix(),
	})

	if err != nil {
		pix_log.Error("[WEBSOCKET] 发送消息失败: %v", err)
		s.RemoveClient(conn)
	} else {
		pix_log.Info("[WEBSOCKET] 设备列表更新发送成功，SessionID: %s，页码: %d",
			clientState.SessionID, clientState.CurrentPage)
	}
}

// GetClientStates 获取所有客户端状态信息
func (s *WebSocketService) GetClientStates() []ClientStateInfo {
	fmt.Println("========== GetClientStates 开始 ==========")
	fmt.Printf("========== 时间戳: %s ==========\n", time.Now().Format("2006-01-02 15:04:05.000"))

	s.clientsMutex.RLock()
	defer s.clientsMutex.RUnlock()

	fmt.Printf("========== clients map 长度: %d ==========\n", len(s.clients))
	fmt.Printf("========== 详细连接信息: ==========\n")
	for conn, state := range s.clients {
		fmt.Printf("  🔗 连接: %s | SessionID: %s | 页码: %d | 每页: %d | 连接时间: %s\n",
			conn.RemoteAddr().String(),
			state.SessionID,
			state.CurrentPage,
			state.PageSize,
			state.ConnectedTime.Format("15:04:05"))
	}

	var clientStates []ClientStateInfo
	now := time.Now()

	for _, clientState := range s.clients {
		fmt.Printf("========== 处理客户端: %s ==========\n", clientState.SessionID)
		duration := now.Sub(clientState.ConnectedTime)
		durationStr := formatDuration(duration)

		info := ClientStateInfo{
			SessionID:     clientState.SessionID,
			CurrentPage:   clientState.CurrentPage,
			PageSize:      clientState.PageSize,
			Filters:       clientState.Filters,
			LastUpdate:    clientState.LastUpdate,
			ConnectedTime: clientState.ConnectedTime,
			Duration:      durationStr,
		}
		clientStates = append(clientStates, info)
		fmt.Printf("========== 客户端状态信息: %+v ==========\n", info)
	}

	fmt.Printf("========== GetClientStates 完成，返回 %d 个客户端状态 ==========\n", len(clientStates))
	return clientStates
}

// formatDuration 格式化持续时间
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0f秒", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.0f分钟", d.Minutes())
	} else if d < 24*time.Hour {
		return fmt.Sprintf("%.1f小时", d.Hours())
	} else {
		return fmt.Sprintf("%.1f天", d.Hours()/24)
	}
}

// SendClientStatesUpdate 发送客户端状态更新
func (s *WebSocketService) SendClientStatesUpdate() {
	fmt.Println("========== SendClientStatesUpdate 开始 ==========")
	clientStates := s.GetClientStates()
	fmt.Printf("========== 获取到客户端状态数量: %d ==========\n", len(clientStates))

	response := map[string]interface{}{
		"client_states": clientStates,
		"total_clients": len(clientStates),
		"timestamp":     time.Now().Unix(),
	}

	fmt.Printf("========== 准备广播客户端状态更新，响应数据: %+v ==========\n", response)
	s.BroadcastMessage("client_states_update", response)
	fmt.Println("========== SendClientStatesUpdate 完成 ==========")
}



// 全局WebSocket服务实例
var GlobalWebSocketService *WebSocketService

// InitWebSocketService 初始化WebSocket服务
func InitWebSocketService() {
	pix_log.Info("[WEBSOCKET] ========== 开始初始化WebSocket服务 ==========")
	pix_log.Info("[WEBSOCKET] 调用 NewWebSocketService()")
	GlobalWebSocketService = NewWebSocketService()
	pix_log.Info("[WEBSOCKET] WebSocket服务实例创建完成，开始启动服务")
	GlobalWebSocketService.Start()
	pix_log.Info("[WEBSOCKET] WebSocket服务启动完成")
	pix_log.Info("[WEBSOCKET] GlobalWebSocketService 地址: %p", GlobalWebSocketService)
	pix_log.Info("[WEBSOCKET] ========== WebSocket服务初始化完成 ==========")
}
