package tdengine

import (
	"ccserver/app/vars"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

// TDengineClient TDengine REST API客户端
type TDengineClient struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	BaseURL  string
}

// DeviceStatus 设备状态结构
type DeviceStatus struct {
	IMSI             string    `json:"imsi"`
	DeviceName       string    `json:"device_name"`
	CurrentStatus    int       `json:"current_status"`    // 0=离线, 1=在线
	LastHeartbeatTime time.Time `json:"last_heartbeat_time"`
	OnlineDuration   int64     `json:"online_duration"`   // 在线时长（秒）
	Position         Position  `json:"position"`
	Speed            int       `json:"speed"`             // 速度 km/h
	Mileage          int       `json:"mileage"`           // 里程 km
	WorkMode         int       `json:"work_mode"`         // 工作模式
	TaskStatus       TaskInfo  `json:"task_status"`
	HeartbeatDelay   int       `json:"heartbeat_delay"`   // 心跳延迟（秒）
	DeviceType       int       `json:"device_type"`       // 设备类型
	BatteryLevel     int       `json:"battery_level"`     // 电池电量
	SignalStrength   int       `json:"signal_strength"`   // 信号强度
	Temperature      int       `json:"temperature"`       // 温度
	ErrorCount       int       `json:"error_count"`       // 错误计数
	LastMaintenanceTime time.Time `json:"last_maintenance_time"` // 最后维护时间
	FirmwareVersion  string    `json:"firmware_version"`  // 固件版本
}

// StatusChangeEvent 状态变化事件
type StatusChangeEvent struct {
	Timestamp   time.Time `json:"timestamp"`   // 状态变化时间
	Status      int       `json:"status"`      // 状态：0=离线, 1=在线
	StatusText  string    `json:"status_text"` // 状态文本
	Duration    int64     `json:"duration"`    // 持续时长（秒）
	Reason      string    `json:"reason"`      // 变化原因
	Description string    `json:"description"` // 详细描述
}

// TaskTimelineStep 任务时间轴步骤
type TaskTimelineStep struct {
	Step int    `json:"step"` // 步骤编号
	Time string `json:"time"` // 时间
}

// TaskTimeline 任务时间轴
type TaskTimeline struct {
	TaskID int                 `json:"taskId"` // 任务ID
	Steps  []*TaskTimelineStep `json:"steps"`  // 步骤列表
}

// Position GPS位置信息
type Position struct {
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	LocationName string  `json:"location_name"`
}

// TaskInfo 任务信息
type TaskInfo struct {
	TaskID         int    `json:"task_id"`
	Step           int    `json:"step"`           // 0=空闲, 1=执行中, 2=完成
	Progress       int    `json:"progress"`       // 进度百分比
	LeftTime       int    `json:"left_time"`      // 剩余时间（秒）
	CurrentStation string `json:"current_station"`
	TargetStation  string `json:"target_station"`
	TaskDistance   int    `json:"task_distance"`  // 任务距离（米）
	StartTime      int64  `json:"start_time"`     // 开始时间戳
}

// TDengineResponse TDengine REST API响应结构 (3.x版本)
type TDengineResponse struct {
	Code       int             `json:"code"`        // TDengine 3.x使用code字段
	ColumnMeta [][]interface{} `json:"column_meta"` // 列元数据
	Data       [][]interface{} `json:"data"`        // 数据行
	Rows       int             `json:"rows"`        // 行数

	// 兼容2.x版本的字段
	Status string   `json:"status,omitempty"` // 2.x版本使用
	Head   []string `json:"head,omitempty"`   // 2.x版本使用
}

// NewTDengineClient 创建TDengine客户端
func NewTDengineClient() *TDengineClient {
	config := vars.Config.Database.Tdengine
	client := &TDengineClient{
		Host:     config.Host,
		Port:     config.Port,
		User:     config.User,
		Password: config.Pass,
		Database: config.Name,
	}
	client.BaseURL = fmt.Sprintf("http://%s:%d/rest/sql", client.Host, client.Port)
	return client
}

// executeSQL 执行SQL查询
func (c *TDengineClient) executeSQL(sql string) (*TDengineResponse, error) {
	pix_log.Info("[TDENGINE] 执行SQL: %s", sql)

	// 使用正确的TDengine 3.x REST API格式
	req, err := http.NewRequest("POST", c.BaseURL, strings.NewReader(sql))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头 - TDengine 3.x使用Basic认证
	req.Header.Set("Content-Type", "text/plain")
	req.SetBasicAuth(c.User, c.Password)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	pix_log.Info("[TDENGINE] 响应内容: %s", string(body))

	// 解析响应 - TDengine 3.x使用不同的响应格式
	var tdResp TDengineResponse
	if err := json.Unmarshal(body, &tdResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v, 响应内容: %s", err, string(body))
	}

	// TDengine 3.x使用code字段而不是status字段
	if tdResp.Code != 0 {
		return nil, fmt.Errorf("TDengine查询失败: %s", string(body))
	}

	pix_log.Info("[TDENGINE] 查询成功，返回 %d 行数据", tdResp.Rows)
	return &tdResp, nil
}

// GetDeviceTables 获取所有设备心跳表
func (c *TDengineClient) GetDeviceTables() ([]string, error) {
	// 使用正确的TDengine 3.3.3语法查询表
	sql := fmt.Sprintf("SHOW %s.TABLES LIKE 'device_heartbeat_%%'", c.Database)
	resp, err := c.executeSQL(sql)
	if err != nil {
		return nil, err
	}

	var tables []string
	for _, row := range resp.Data {
		if len(row) > 0 {
			if tableName, ok := row[0].(string); ok {
				tables = append(tables, tableName)
			}
		}
	}

	pix_log.Info("[TDENGINE] 找到 %d 个设备表", len(tables))
	return tables, nil
}

// GetLatestDeviceStatus 获取设备最新状态
func (c *TDengineClient) GetLatestDeviceStatus(tableName string) (*DeviceStatus, error) {
	// 提取IMSI（从表名中提取）
	imsi := strings.TrimPrefix(tableName, "device_heartbeat_")
	
	sql := fmt.Sprintf(`
		SELECT 
			ts, lat, lng, spd, tm, mode, latest_task
		FROM %s.%s 
		ORDER BY ts DESC 
		LIMIT 1
	`, c.Database, tableName)

	resp, err := c.executeSQL(sql)
	if err != nil {
		return nil, err
	}

	if len(resp.Data) == 0 {
		return nil, fmt.Errorf("设备 %s 无数据", imsi)
	}

	// 调试日志：打印 TDengine 返回的原始数据
	pix_log.InfoWithIMSI(imsi, "[TDENGINE] 查询结果: %+v", resp.Data[0])
	if len(resp.Data[0]) > 0 {
		pix_log.InfoWithIMSI(imsi, "[TDENGINE] ts 字段原始值: %v (类型: %T)", resp.Data[0][0], resp.Data[0][0])
	}

	row := resp.Data[0]
	if len(row) < 7 {
		return nil, fmt.Errorf("设备 %s 数据格式不正确", imsi)
	}

	// 解析时间戳 - 支持 TDengine 多种时间格式
	var lastHeartbeat time.Time
	if tsStr, ok := row[0].(string); ok {
		// TDengine 返回的时间格式（按优先级排序）
		timeFormats := []string{
			"2006-01-02T15:04:05.000Z",     // TDengine 标准格式（优先）
			"2006-01-02T15:04:05Z",         // ISO 格式（无毫秒）
			"2006-01-02T15:04:05.000000Z",  // ISO 微秒格式
			time.RFC3339,                   // 标准 RFC3339
			time.RFC3339Nano,               // RFC3339 纳秒
			"2006-01-02 15:04:05.000",      // 毫秒精度（备用）
			"2006-01-02 15:04:05.000000",   // 微秒精度（备用）
			"2006-01-02 15:04:05",          // 秒精度（备用）
		}

		var parseErr error
		for _, format := range timeFormats {
			if ts, err := time.Parse(format, tsStr); err == nil {
				lastHeartbeat = ts
				pix_log.DebugWithIMSI(imsi, "[TDENGINE] 成功解析时间戳: %s -> %v", tsStr, lastHeartbeat)
				break
			} else {
				parseErr = err
			}
		}

		// 如果所有格式都失败，记录错误
		if lastHeartbeat.IsZero() {
			pix_log.ErrorWithIMSI(imsi, "[TDENGINE] 解析时间戳失败: %s, 最后错误: %v", tsStr, parseErr)
		}
	} else {
		pix_log.ErrorWithIMSI(imsi, "[TDENGINE] ts 字段类型错误: %T, 值: %v", row[0], row[0])
	}

	// 解析GPS坐标
	var lat, lng float64
	if latVal, ok := row[1].(float64); ok {
		lat = latVal
	}
	if lngVal, ok := row[2].(float64); ok {
		lng = lngVal
	}

	// 解析其他字段
	var speed, mileage, mode int
	if spdVal, ok := row[3].(float64); ok {
		speed = int(spdVal)
	}
	if tmVal, ok := row[4].(float64); ok {
		mileage = int(tmVal)
	}
	if modeVal, ok := row[5].(float64); ok {
		mode = int(modeVal)
	}

	// 解析任务信息JSON
	var taskInfo TaskInfo
	if latestTaskStr, ok := row[6].(string); ok && latestTaskStr != "" {
		var taskData map[string]interface{}
		if err := json.Unmarshal([]byte(latestTaskStr), &taskData); err == nil {
			if taskID, ok := taskData["task_id"].(float64); ok {
				taskInfo.TaskID = int(taskID)
			}
			if step, ok := taskData["step"].(float64); ok {
				taskInfo.Step = int(step)
			}
			if progress, ok := taskData["progress"].(float64); ok {
				taskInfo.Progress = int(progress)
			}
			if leftTime, ok := taskData["left_time"].(float64); ok {
				taskInfo.LeftTime = int(leftTime)
			}
			if currentStation, ok := taskData["current_station"].(string); ok {
				taskInfo.CurrentStation = currentStation
			}
			if targetStation, ok := taskData["target_station"].(string); ok {
				taskInfo.TargetStation = targetStation
			}
		}
	}

	// 判断在线状态（基于心跳时间）
	currentStatus := 0
	heartbeatTimeout := vars.Config.Business.HeartbeatTimeout
	if time.Since(lastHeartbeat) <= heartbeatTimeout {
		currentStatus = 1
	}

	// 计算在线时长
	onlineDuration := int64(0)
	if currentStatus == 1 {
		onlineDuration = int64(time.Since(lastHeartbeat).Seconds())
	}

	// 生成位置名称（简化版）
	locationName := fmt.Sprintf("位置(%.4f,%.4f)", lat, lng)

	device := &DeviceStatus{
		IMSI:              imsi,
		DeviceName:        fmt.Sprintf("设备-%s", imsi),
		CurrentStatus:     currentStatus,
		LastHeartbeatTime: lastHeartbeat,
		OnlineDuration:    onlineDuration,
		Position: Position{
			Latitude:     lat,
			Longitude:    lng,
			LocationName: locationName,
		},
		Speed:               speed,
		Mileage:             mileage,
		WorkMode:            mode,
		TaskStatus:          taskInfo,
		HeartbeatDelay:      int(time.Since(lastHeartbeat).Seconds()),
		DeviceType:          0,
		BatteryLevel:        85 + (int(imsi[len(imsi)-1:][0]) % 15), // 模拟电量
		SignalStrength:      3 + (int(imsi[len(imsi)-1:][0]) % 3),   // 模拟信号强度
		Temperature:         20 + (int(imsi[len(imsi)-1:][0]) % 20), // 模拟温度
		ErrorCount:          0,
		LastMaintenanceTime: time.Now().AddDate(0, 0, -7), // 7天前维护
		FirmwareVersion:     "v2.1.0",
	}

	return device, nil
}

// GetAllDevicesStatus 获取所有设备状态
func (c *TDengineClient) GetAllDevicesStatus() ([]*DeviceStatus, error) {
	tables, err := c.GetDeviceTables()
	if err != nil {
		return nil, err
	}

	var devices []*DeviceStatus
	for _, table := range tables {
		device, err := c.GetLatestDeviceStatus(table)
		if err != nil {
			pix_log.Warning("[TDENGINE] 获取设备状态失败: %s, 错误: %v", table, err)
			continue
		}
		devices = append(devices, device)
	}

	pix_log.Info("[TDENGINE] 成功获取 %d 个设备状态", len(devices))
	return devices, nil
}

// SearchDevicesByIMSI 根据IMSI搜索设备（模糊匹配）
func (c *TDengineClient) SearchDevicesByIMSI(searchTerm string) ([]*DeviceStatus, error) {
	tables, err := c.GetDeviceTables()
	if err != nil {
		return nil, err
	}

	var devices []*DeviceStatus
	searchLower := strings.ToLower(searchTerm)

	for _, table := range tables {
		// 从表名中提取IMSI
		imsi := strings.TrimPrefix(table, "device_heartbeat_")

		// 检查IMSI是否包含搜索词（忽略大小写）
		if strings.Contains(strings.ToLower(imsi), searchLower) {
			device, err := c.GetLatestDeviceStatus(table)
			if err != nil {
				pix_log.Warning("[TDENGINE] 获取设备状态失败: %s, 错误: %v", table, err)
				continue
			}
			devices = append(devices, device)
		}
	}

	pix_log.Info("[TDENGINE] 搜索 '%s' 匹配到 %d 个设备", searchTerm, len(devices))
	return devices, nil
}

// GetAllIMSIList 获取所有IMSI列表
func (c *TDengineClient) GetAllIMSIList() ([]string, error) {
	tables, err := c.GetDeviceTables()
	if err != nil {
		return nil, err
	}

	var imsiList []string
	for _, table := range tables {
		// 从表名中提取IMSI（表名格式：device_heartbeat_IMSI）
		imsi := strings.TrimPrefix(table, "device_heartbeat_")
		if imsi != "" && imsi != table { // 确保成功提取到IMSI
			imsiList = append(imsiList, imsi)
		}
	}

	pix_log.Info("[TDENGINE] 成功获取 %d 个IMSI", len(imsiList))
	return imsiList, nil
}

// GetDeviceHistory 获取设备历史数据
func (c *TDengineClient) GetDeviceHistory(imsi string, startTime, endTime time.Time, limit int) ([]*DeviceStatus, error) {
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)
	
	sql := fmt.Sprintf(`
		SELECT 
			ts, lat, lng, spd, tm, mode, latest_task
		FROM %s.%s 
		WHERE ts >= '%s' AND ts <= '%s'
		ORDER BY ts DESC 
		LIMIT %d
	`, c.Database, tableName, 
		startTime.Format("2006-01-02 15:04:05"), 
		endTime.Format("2006-01-02 15:04:05"), 
		limit)

	resp, err := c.executeSQL(sql)
	if err != nil {
		return nil, err
	}

	var devices []*DeviceStatus
	for _, row := range resp.Data {
		if len(row) < 7 {
			continue
		}

		// 解析数据（类似GetLatestDeviceStatus的逻辑）
		var lastHeartbeat time.Time
		if tsStr, ok := row[0].(string); ok {
			if ts, err := time.Parse("2006-01-02 15:04:05.000", tsStr); err == nil {
				lastHeartbeat = ts
			}
		}

		var lat, lng float64
		if latVal, ok := row[1].(float64); ok {
			lat = latVal
		}
		if lngVal, ok := row[2].(float64); ok {
			lng = lngVal
		}

		var speed, mileage, mode int
		if spdVal, ok := row[3].(float64); ok {
			speed = int(spdVal)
		}
		if tmVal, ok := row[4].(float64); ok {
			mileage = int(tmVal)
		}
		if modeVal, ok := row[5].(float64); ok {
			mode = int(modeVal)
		}

		var taskInfo TaskInfo
		if latestTaskStr, ok := row[6].(string); ok && latestTaskStr != "" {
			var taskData map[string]interface{}
			if err := json.Unmarshal([]byte(latestTaskStr), &taskData); err == nil {
				if taskID, ok := taskData["task_id"].(float64); ok {
					taskInfo.TaskID = int(taskID)
				}
				if step, ok := taskData["step"].(float64); ok {
					taskInfo.Step = int(step)
				}
			}
		}

		currentStatus := 0
		heartbeatTimeout := vars.Config.Business.HeartbeatTimeout
		if time.Since(lastHeartbeat) <= heartbeatTimeout {
			currentStatus = 1
		}

		device := &DeviceStatus{
			IMSI:              imsi,
			DeviceName:        fmt.Sprintf("设备-%s", imsi),
			CurrentStatus:     currentStatus,
			LastHeartbeatTime: lastHeartbeat,
			Position: Position{
				Latitude:     lat,
				Longitude:    lng,
				LocationName: fmt.Sprintf("位置(%.4f,%.4f)", lat, lng),
			},
			Speed:      speed,
			Mileage:    mileage,
			WorkMode:   mode,
			TaskStatus: taskInfo,
		}

		devices = append(devices, device)
	}

	return devices, nil
}

// GetDeviceStatusHistory 获取设备状态变化历史
func (c *TDengineClient) GetDeviceStatusHistory(imsi string, startTime, endTime time.Time) ([]*StatusChangeEvent, error) {
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)

	// 查询指定时间范围内的所有心跳数据
	sql := fmt.Sprintf(`
		SELECT ts
		FROM %s.%s
		WHERE ts >= '%s' AND ts <= '%s'
		ORDER BY ts ASC
	`, c.Database, tableName,
		startTime.Format("2006-01-02 15:04:05"),
		endTime.Format("2006-01-02 15:04:05"))

	resp, err := c.executeSQL(sql)
	if err != nil {
		return nil, err
	}

	if len(resp.Data) == 0 {
		return []*StatusChangeEvent{}, nil
	}

	// 解析心跳时间戳
	var heartbeats []time.Time
	for _, row := range resp.Data {
		if len(row) > 0 {
			if tsStr, ok := row[0].(string); ok {
				// 使用与GetLatestDeviceStatus相同的时间解析逻辑
				timeFormats := []string{
					"2006-01-02T15:04:05.000Z",
					"2006-01-02T15:04:05Z",
					"2006-01-02T15:04:05.000000Z",
					time.RFC3339,
					time.RFC3339Nano,
					"2006-01-02 15:04:05.000",
					"2006-01-02 15:04:05.000000",
					"2006-01-02 15:04:05",
				}

				for _, format := range timeFormats {
					if ts, err := time.Parse(format, tsStr); err == nil {
						heartbeats = append(heartbeats, ts)
						break
					}
				}
			}
		}
	}

	if len(heartbeats) == 0 {
		return []*StatusChangeEvent{}, nil
	}

	// 分析状态变化
	return c.analyzeStatusChanges(imsi, heartbeats, startTime, endTime), nil
}

// analyzeStatusChanges 分析心跳数据中的状态变化
func (c *TDengineClient) analyzeStatusChanges(imsi string, heartbeats []time.Time, startTime, endTime time.Time) []*StatusChangeEvent {
	if len(heartbeats) == 0 {
		return []*StatusChangeEvent{}
	}

	var events []*StatusChangeEvent
	heartbeatTimeout := vars.Config.Business.HeartbeatTimeout

	pix_log.InfoWithIMSI(imsi, "[TDENGINE] 开始分析状态变化: 心跳数=%d, 超时时间=%v", len(heartbeats), heartbeatTimeout)

	// 检查查询开始时间之前的状态
	firstHeartbeat := heartbeats[0]
	if startTime.Before(firstHeartbeat) {
		// 如果查询开始时间早于第一个心跳，说明开始时是离线状态
		if firstHeartbeat.Sub(startTime) > heartbeatTimeout {
			// 添加离线状态事件
			events = append(events, &StatusChangeEvent{
				Timestamp:   startTime,
				Status:      0,
				StatusText:  "离线",
				Duration:    int64(firstHeartbeat.Sub(startTime).Seconds()),
				Reason:      "查询期间开始",
				Description: "查询开始时设备处于离线状态",
			})
		}

		// 添加上线事件
		events = append(events, &StatusChangeEvent{
			Timestamp:   firstHeartbeat,
			Status:      1,
			StatusText:  "在线",
			Duration:    0, // 将在后续计算
			Reason:      "心跳恢复",
			Description: fmt.Sprintf("设备开始发送心跳，最后心跳: %s", firstHeartbeat.Format("2006-01-02 15:04:05")),
		})
	}

	// 分析连续心跳之间的间隔
	for i := 1; i < len(heartbeats); i++ {
		prevHeartbeat := heartbeats[i-1]
		currHeartbeat := heartbeats[i]
		interval := currHeartbeat.Sub(prevHeartbeat)

		// 如果间隔超过心跳超时时间，说明中间有离线期间
		if interval > heartbeatTimeout {
			// 计算离线开始时间（上次心跳 + 超时时间）
			offlineStart := prevHeartbeat.Add(heartbeatTimeout)
			offlineDuration := currHeartbeat.Sub(offlineStart)

			// 添加离线事件
			events = append(events, &StatusChangeEvent{
				Timestamp:   offlineStart,
				Status:      0,
				StatusText:  "离线",
				Duration:    int64(offlineDuration.Seconds()),
				Reason:      "心跳超时",
				Description: fmt.Sprintf("心跳间隔 %.1f 分钟超过超时时间 %.1f 分钟",
					interval.Minutes(), heartbeatTimeout.Minutes()),
			})

			// 添加上线事件
			events = append(events, &StatusChangeEvent{
				Timestamp:   currHeartbeat,
				Status:      1,
				StatusText:  "在线",
				Duration:    0, // 将在后续计算
				Reason:      "心跳恢复",
				Description: fmt.Sprintf("设备恢复心跳，离线时长: %.1f 分钟", offlineDuration.Minutes()),
			})
		}
	}

	// 检查最后一个心跳到查询结束时间的状态
	lastHeartbeat := heartbeats[len(heartbeats)-1]
	if endTime.After(lastHeartbeat) {
		timeSinceLastHeartbeat := endTime.Sub(lastHeartbeat)
		if timeSinceLastHeartbeat > heartbeatTimeout {
			// 设备在查询结束前已经离线
			offlineStart := lastHeartbeat.Add(heartbeatTimeout)
			offlineDuration := endTime.Sub(offlineStart)

			events = append(events, &StatusChangeEvent{
				Timestamp:   offlineStart,
				Status:      0,
				StatusText:  "离线",
				Duration:    int64(offlineDuration.Seconds()),
				Reason:      "心跳超时",
				Description: fmt.Sprintf("最后心跳: %s，超时后离线", lastHeartbeat.Format("2006-01-02 15:04:05")),
			})
		}
	}

	// 计算在线状态的持续时长
	for i := 0; i < len(events); i++ {
		if events[i].Status == 1 { // 在线状态
			// 查找下一个离线事件
			var nextOfflineTime time.Time
			found := false
			for j := i + 1; j < len(events); j++ {
				if events[j].Status == 0 { // 找到下一个离线事件
					nextOfflineTime = events[j].Timestamp
					found = true
					break
				}
			}

			if found {
				events[i].Duration = int64(nextOfflineTime.Sub(events[i].Timestamp).Seconds())
			} else {
				// 如果没有找到下一个离线事件，计算到查询结束时间
				events[i].Duration = int64(endTime.Sub(events[i].Timestamp).Seconds())
			}
		}
	}

	pix_log.InfoWithIMSI(imsi, "[TDENGINE] 状态分析完成: 发现 %d 个状态变化事件", len(events))
	return events
}

// GetLastOnlineTime 获取设备最后一次在线时间
func (c *TDengineClient) GetLastOnlineTime(imsi string) (*time.Time, error) {
	// 获取最新的心跳时间
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)
	device, err := c.GetLatestDeviceStatus(tableName)
	if err != nil {
		return nil, err
	}

	heartbeatTimeout := vars.Config.Business.HeartbeatTimeout
	lastHeartbeat := device.LastHeartbeatTime

	// 如果当前是在线状态，返回当前时间
	if time.Since(lastHeartbeat) <= heartbeatTimeout {
		now := time.Now()
		return &now, nil
	}

	// 如果当前是离线状态，最后在线时间是：最后心跳时间 + 超时时间
	lastOnlineTime := lastHeartbeat.Add(heartbeatTimeout)
	return &lastOnlineTime, nil
}

// GetDeviceStatusSummary 获取设备状态摘要（包含最后在线时间）
func (c *TDengineClient) GetDeviceStatusSummary(imsi string) (map[string]interface{}, error) {
	// 获取设备当前状态
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)
	device, err := c.GetLatestDeviceStatus(tableName)
	if err != nil {
		return nil, err
	}

	// 获取最后在线时间
	lastOnlineTime, err := c.GetLastOnlineTime(imsi)
	if err != nil {
		return nil, err
	}

	// 计算离线时长
	var offlineDuration int64 = 0
	if device.CurrentStatus == 0 && lastOnlineTime != nil {
		offlineDuration = int64(time.Since(*lastOnlineTime).Seconds())
	}

	summary := map[string]interface{}{
		"imsi":                imsi,
		"current_status":      device.CurrentStatus,
		"current_status_text": map[int]string{0: "离线", 1: "在线"}[device.CurrentStatus],
		"last_heartbeat_time": device.LastHeartbeatTime,
		"last_online_time":    lastOnlineTime,
		"offline_duration":    offlineDuration,
		"offline_duration_text": func() string {
			if offlineDuration == 0 {
				return "当前在线"
			}
			hours := offlineDuration / 3600
			minutes := (offlineDuration % 3600) / 60
			if hours > 0 {
				return fmt.Sprintf("%d小时%d分钟前", hours, minutes)
			}
			return fmt.Sprintf("%d分钟前", minutes)
		}(),
	}

	pix_log.InfoWithIMSI(imsi, "[TDENGINE] 状态摘要: 当前状态=%s, 最后在线=%v, 离线时长=%d秒",
		summary["current_status_text"], lastOnlineTime, offlineDuration)

	return summary, nil
}

// GetLatestStatusChange 获取设备最近的一次状态变化
func (c *TDengineClient) GetLatestStatusChange(imsi string) (*StatusChangeEvent, error) {
	// 获取最近24小时的状态历史
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -1) // 最近24小时

	statusHistory, err := c.GetDeviceStatusHistory(imsi, startTime, endTime)
	if err != nil {
		return nil, err
	}

	if len(statusHistory) == 0 {
		return nil, nil
	}

	// 返回最近的一次状态变化
	latestEvent := statusHistory[len(statusHistory)-1]

	pix_log.InfoWithIMSI(imsi, "[TDENGINE] 最近状态变化: %s at %s",
		latestEvent.StatusText, latestEvent.Timestamp.Format("2006-01-02 15:04:05"))

	return latestEvent, nil
}

// GetTaskTimeline 获取任务时间轴
func (c *TDengineClient) GetTaskTimeline(imsi string, date string, taskId int) (*TaskTimeline, error) {
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)

	// 构建查询SQL - 查询指定日期和任务ID的数据
	sql := fmt.Sprintf(`
		SELECT ts, latest_task
		FROM %s.%s
		WHERE ts >= '%s 00:00:00'
		  AND ts <= '%s 23:59:59'
		  AND latest_task LIKE '%%"taskId":%d%%'
		  AND latest_task NOT LIKE '%%"taskId":0%%'
		ORDER BY ts ASC
	`, c.Database, tableName, date, date, taskId)

	pix_log.Info("[TDENGINE] 查询任务时间轴: IMSI=%s, Date=%s, TaskID=%d", imsi, date, taskId)
	pix_log.Info("[TDENGINE] SQL: %s", sql)

	resp, err := c.executeSQL(sql)
	if err != nil {
		return nil, fmt.Errorf("查询任务时间轴失败: %v", err)
	}

	if len(resp.Data) == 0 {
		pix_log.Info("[TDENGINE] 未找到任务时间轴数据: IMSI=%s, Date=%s, TaskID=%d", imsi, date, taskId)
		return &TaskTimeline{
			TaskID: taskId,
			Steps:  []*TaskTimelineStep{},
		}, nil
	}

	// 用于存储每个step的最早时间
	stepTimeMap := make(map[int]string)

	// 解析查询结果
	for _, row := range resp.Data {
		if len(row) < 2 {
			continue
		}

		// 解析时间戳
		var timestamp string
		if tsStr, ok := row[0].(string); ok {
			// 解析时间并格式化为标准格式
			timeFormats := []string{
				"2006-01-02T15:04:05.000Z",
				"2006-01-02T15:04:05Z",
				"2006-01-02T15:04:05.000000Z",
				time.RFC3339,
				time.RFC3339Nano,
				"2006-01-02 15:04:05.000",
				"2006-01-02 15:04:05.000000",
				"2006-01-02 15:04:05",
			}

			for _, format := range timeFormats {
				if ts, err := time.Parse(format, tsStr); err == nil {
					timestamp = ts.Format("2006-01-02 15:04:05")
					break
				}
			}
		}

		if timestamp == "" {
			continue
		}

		// 解析latest_task JSON
		if latestTaskStr, ok := row[1].(string); ok && latestTaskStr != "" {
			var taskData map[string]interface{}
			if err := json.Unmarshal([]byte(latestTaskStr), &taskData); err == nil {
				// 验证taskId是否匹配
				if taskID, ok := taskData["taskId"].(float64); ok && int(taskID) == taskId {
					// 提取step
					if stepVal, ok := taskData["step"].(float64); ok {
						step := int(stepVal)

						// 只保留每个step的最早时间
						if existingTime, exists := stepTimeMap[step]; !exists {
							stepTimeMap[step] = timestamp
						} else {
							// 比较时间，保留更早的
							if timestamp < existingTime {
								stepTimeMap[step] = timestamp
							}
						}
					}
				}
			}
		}
	}

	// 构建时间轴步骤列表
	var steps []*TaskTimelineStep
	for step, timeStr := range stepTimeMap {
		steps = append(steps, &TaskTimelineStep{
			Step: step,
			Time: timeStr,
		})
	}

	// 按step排序
	for i := 0; i < len(steps)-1; i++ {
		for j := i + 1; j < len(steps); j++ {
			if steps[i].Step > steps[j].Step {
				steps[i], steps[j] = steps[j], steps[i]
			}
		}
	}

	timeline := &TaskTimeline{
		TaskID: taskId,
		Steps:  steps,
	}

	pix_log.Info("[TDENGINE] 任务时间轴查询成功: IMSI=%s, Date=%s, TaskID=%d, Steps=%d",
		imsi, date, taskId, len(steps))

	return timeline, nil
}
