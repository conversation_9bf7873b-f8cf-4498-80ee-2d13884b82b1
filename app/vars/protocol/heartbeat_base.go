package protocol

// HeartbeatBase 心跳数据基础结构,包含所有共用字段
type HeartbeatBase struct {
	// 网络状态
	Lan             []LanStatus `json:"lan"`     // 网络连接状态列表
	UpgradeState    string      `json:"state"`   // 升级状态
	UpgradeProgress string      `json:"percent"` // 升级进度

	// 位置信息
	Latitude  float64 `json:"lat"`      // 纬度
	Longitude float64 `json:"lng"`      // 经度
	Position  string  `json:"position"` // 位置描述

	// 状态信息
	Located       int8    `json:"located"`   // 定位状态(0:未定位 1:已定位)
	Status        int8    `json:"sta"`       // 设备状态
	Mode          int8    `json:"mode"`      // 工作模式
	ACC           int8    `json:"acc"`       // ACC状态
	Gear          int8    `json:"gear"`      // 档位状态
	Door          int8    `json:"door"`      // 车门状态
	Light         int8    `json:"light"`     // 车灯状态
	Window        int8    `json:"win"`       // 车窗状态
	TotalMileage  int32   `json:"tm"`        // 总里程（km)
	RemainMileage int16   `json:"rm"`        // 剩余里程(km)
	SteeringAngle float32 `json:"ste"`       // 转向角度
	BrakeStatus   float32 `json:"brk"`       // 刹车状态
	HeadMode      int8    `json:"head_mode"` // 大灯模式
	Signal        int8    `json:"sgn"`       // 转向信号
	Speed         int16   `json:"spd"`       // 当前速度
	SpeedLimit    int16   `json:"spdL"`      // 限速值
	PowerLevel    int8    `json:"pL"`        // 电量等级
	PowerVoltage  int16   `json:"pV"`        // 电压
	PowerCurrent  int16   `json:"pC"`        // 电流
	PowerCharging int8    `json:"pCh"`       // 充电状态
	BatteryStatus int16   `json:"bat"`       // 电池状态
	Altitude      float32 `json:"alt"`       // 海拔高度
	Angle         float32 `json:"angle"`     // 方向角度
	SatCount      int8    `json:"sat_cnt"`   // 卫星数量
	Error         string  `json:"err"`       // 错误信息
	Event         uint8   `json:"event"`     // 事件类型
	HeadLight     int     `json:"headLight"` // 前照灯状态

	// 车辆状态
	VType           int8    `json:"vType"`       // 车辆类型
	VehicleStatus   int8    `json:"vSta"`        // 车辆状态
	IPCToChannel    bool    `json:"ipcToCha"`    // IPC通道状态
	Throttle        float32 `json:"thr"`         // 油门状态
	ADLocated       bool    `json:"adLocated"`   // AD定位状态
	GPSLocated      bool    `json:"gpsLocated"`  // GPS定位状态
	GPSStrength     int8    `json:"gpsStrength"` // GPS信号强度
	Shutdown        int8    `json:"shutdown"`    // 关机状态
	EmergencyStatus int8    `json:"emgSta"`      // 紧急状态

	// 环境状态
	AirCondition     int8  `json:"airCon"`   // 空调状态
	InnerTemperature int32 `json:"inTemp"`   // 车内温度
	OuterTemperature int32 `json:"outTemp"`  // 车外温度
	Smoke            int32 `json:"smoke"`    // 烟雾浓度
	CO2              int32 `json:"co2"`      // 二氧化碳浓度
	Seatbelt         int   `json:"seatbelt"` // 安全带状态

	// 任务相关
	LatestTask  TaskInfo    `json:"latestTask"` // 最新任务信息
	RetailBot   RetailBot   `json:"retailBot"`  // 零售机器人信息
	AlarmCount1 []int       `json:"alarmCnt1"`  // 警报计数1
	AlarmCount2 []int       `json:"alarmCnt2"`  // 警报计数2
	AlarmList   []AlarmInfo `json:"alarmList"`  // 警报信息
	Alarm       AlarmInfo   `json:"alarm"`      // 警报信息
	RC          int         `json:"rc"`         // 遥控状态

	// 错误状态
	BotStatus []string `json:"botStatus"` // 机器人状态列表
	BotErr    []string `json:"botErr"`    // 机器人错误列表
	CErr      []string `json:"cErr"`      // 控制错误列表
	CM        int32    `json:"cm"`        // CM状态值（新增字段）
	AutoErr   []string `json:"autoErr"`   // 自动驾驶错误列表

	// 其他状态
	PosQuality float32 `json:"pos_q"`   // 定位质量
	Warning    []int   `json:"warning"` // 警告信息列表

	// 座位状态
	Seat [6]int `json:"seat"` // 座位状态数组(最多6个座位)
}
