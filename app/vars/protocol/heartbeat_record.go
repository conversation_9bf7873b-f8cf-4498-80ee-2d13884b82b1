// app/vars/protocol/heartbeat_record.go

package protocol

// HeartbeatRecord MQTT消息用的心跳数据结构
type HeartbeatRecord struct {
	// 基本标识字段
	DeviceID int64  `json:"device_id"`
	IMSI     string `json:"imsi"`
	UID      int64  `json:"uid"`
	Ts       int64  `json:"ts"`

	// 网络状态
	Lan1Status      int64  `json:"lan1_status"`
	Lan2Status      int64  `json:"lan2_status"`
	UpgradeState    string `json:"upgrade_state"`
	UpgradeProgress string `json:"upgrade_progress"`

	// 位置信息
	Latitude   float64 `json:"lat"`
	Longitude  float64 `json:"lng"`
	Located    int8    `json:"located"`
	PosQuality float64 `json:"pos_q"`
	Altitude   float64 `json:"alt"`
	Angle      float64 `json:"angle"`
	SatCount   int32   `json:"sat_cnt"`
	Seat       [6]int  `json:"seat"`

	// 车辆基本状态
	Status int8   `json:"sta"`
	Mode   int32  `json:"mode"`
	Error  string `json:"err"`
	Event  uint8  `json:"event"`

	// 车辆控制状态
	ACC             int32   `json:"acc"`
	Gear            int32   `json:"gear"`
	Door            int32   `json:"door"`
	Light           int32   `json:"light"`
	Window          int32   `json:"win"`
	HeadLight       int32   `json:"head_light"`
	HeadMode        int32   `json:"head_mode"`
	SteeringAngle   float64 `json:"ste"`
	BrakeStatus     float64 `json:"brk"`
	Throttle        float64 `json:"thr"`
	EmergencyStatus int32   `json:"emgSta"`

	// 行驶状态
	TotalMileage  int32 `json:"tm"` // 总里程（km)
	RemainMileage int16 `json:"rm"` // 剩余里程(km)
	Speed         int32 `json:"spd"`
	SpeedLimit    int32 `json:"spdL"`

	// 电源状态
	PowerLevel    int32 `json:"pL"`
	PowerVoltage  int32 `json:"pV"`
	PowerCurrent  int32 `json:"pC"`
	PowerCharging int32 `json:"pCh"`
	BatteryStatus int32 `json:"bat"`

	// 通信状态
	Signal       int32 `json:"sgn"`
	IPCToChannel int8  `json:"ipcToCha"`
	// ADLocated    int8  `json:"adLocated"`
	ADLocated   int8  `json:"adLocated"` // 添加 string 选项
	GPSLocated  int8  `json:"gpsLocated"`
	GPSStrength int32 `json:"gpsStrength"`

	// 环境状态
	AirCondition     int32 `json:"airCon"`
	InnerTemperature int32 `json:"inTemp"`  // 车内温度
	OuterTemperature int32 `json:"outTemp"` // 车外温度
	Smoke            int32 `json:"smoke"`
	CO2              int32 `json:"co2"`
	Seatbelt         int32 `json:"seatbelt"`

	// 车辆类型和状态
	VehicleType   int32 `json:"v_type"`
	VehicleStatus int32 `json:"vSta"`
	Shutdown      int8  `json:"shutdown"`
	RC            int32 `json:"rc"`
	CM            int32 `json:"cm"` // CM状态值（新增字段）

	// 使用 heartbeat_device.go 中定义的结构体类型
	SweeperStatus []uint32    `json:"sweeper_status"`
	LatestTask    TaskInfo    `json:"latest_task"`
	RetailBot     RetailBot   `json:"retail_bot"`
	AlarmCount1   []int       `json:"alarm_cnt1"`
	AlarmCount2   []int       `json:"alarm_cnt2"`
	Alarm         AlarmInfo   `json:"alarm"`
	AlarmList     []AlarmInfo `json:"alarm_list"`
}

// NewHeartbeatRecord 创建新的心跳记录
func NewHeartbeatRecord() *HeartbeatRecord {
	return &HeartbeatRecord{
		AlarmCount1:   make([]int, 0),
		AlarmCount2:   make([]int, 0),
		SweeperStatus: make([]uint32, 0),
	}
}
