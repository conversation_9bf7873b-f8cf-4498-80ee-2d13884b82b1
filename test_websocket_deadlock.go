package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// 测试WebSocket服务的并发安全性和死锁问题
func main() {
	fmt.Println("🚀 开始WebSocket死锁测试...")
	
	// 测试配置
	const (
		serverURL     = "ws://localhost:8080/ws"
		numClients    = 50  // 并发客户端数量
		testDuration  = 30 * time.Second // 测试持续时间
		messageRate   = 100 * time.Millisecond // 消息发送间隔
	)

	var wg sync.WaitGroup
	startTime := time.Now()
	
	// 统计信息
	var (
		connectedClients    int32
		totalMessagesSent   int64
		totalMessagesRecv   int64
		connectionErrors    int32
		messageErrors       int32
	)

	fmt.Printf("📊 测试参数：\n")
	fmt.Printf("   - 并发客户端数: %d\n", numClients)
	fmt.Printf("   - 测试时长: %v\n", testDuration)
	fmt.Printf("   - 消息间隔: %v\n", messageRate)
	fmt.Printf("   - 服务器地址: %s\n", serverURL)
	fmt.Println()

	// 启动多个并发客户端
	for i := 0; i < numClients; i++ {
		wg.Add(1)
		go func(clientID int) {
			defer wg.Done()
			testClient(clientID, serverURL, testDuration, messageRate, 
				&connectedClients, &totalMessagesSent, &totalMessagesRecv, 
				&connectionErrors, &messageErrors)
		}(i)
		
		// 错开连接时间，避免同时连接造成压力
		time.Sleep(10 * time.Millisecond)
	}

	// 等待所有客户端完成
	wg.Wait()
	
	// 输出测试结果
	elapsed := time.Since(startTime)
	fmt.Println("\n" + "="*60)
	fmt.Println("📈 测试结果统计")
	fmt.Println("="*60)
	fmt.Printf("✅ 测试完成时间: %v\n", elapsed)
	fmt.Printf("📡 最大并发连接: %d\n", connectedClients)
	fmt.Printf("📤 总发送消息数: %d\n", totalMessagesSent)
	fmt.Printf("📥 总接收消息数: %d\n", totalMessagesRecv)
	fmt.Printf("❌ 连接错误数: %d\n", connectionErrors)
	fmt.Printf("⚠️  消息错误数: %d\n", messageErrors)
	
	if connectionErrors == 0 && messageErrors == 0 {
		fmt.Println("🎉 测试通过！未发现死锁或连接问题")
	} else {
		fmt.Println("⚠️  测试发现问题，请检查服务器日志")
	}
}

// testClient 模拟单个WebSocket客户端
func testClient(clientID int, serverURL string, duration time.Duration, 
	messageRate time.Duration, connectedClients, totalMessagesSent, 
	totalMessagesRecv, connectionErrors, messageErrors *int64) {
	
	// 解析URL
	u, err := url.Parse(serverURL)
	if err != nil {
		log.Printf("客户端 %d: URL解析失败: %v", clientID, err)
		return
	}

	// 连接WebSocket
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Printf("客户端 %d: 连接失败: %v", clientID, err)
		*connectionErrors++
		return
	}
	defer conn.Close()
	
	*connectedClients++
	fmt.Printf("🔗 客户端 %d 已连接\n", clientID)

	// 设置读写超时
	conn.SetReadDeadline(time.Now().Add(duration + 10*time.Second))
	conn.SetWriteDeadline(time.Now().Add(duration + 10*time.Second))

	// 启动消息接收goroutine
	go func() {
		for {
			_, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("客户端 %d: 读取消息错误: %v", clientID, err)
					*messageErrors++
				}
				return
			}
			*totalMessagesRecv++
			
			// 解析消息类型
			var msg map[string]interface{}
			if err := json.Unmarshal(message, &msg); err == nil {
				if msgType, ok := msg["type"].(string); ok {
					if clientID == 0 && *totalMessagesRecv%100 == 0 {
						fmt.Printf("📥 客户端 %d 收到消息类型: %s\n", clientID, msgType)
					}
				}
			}
		}
	}()

	// 发送测试消息
	ticker := time.NewTicker(messageRate)
	defer ticker.Stop()
	
	endTime := time.Now().Add(duration)
	messageCount := 0
	
	for time.Now().Before(endTime) {
		select {
		case <-ticker.C:
			// 发送不同类型的消息来测试各种场景
			var message map[string]interface{}
			
			switch messageCount % 4 {
			case 0:
				// 请求客户端状态
				message = map[string]interface{}{
					"type": "get_client_states",
					"data": nil,
				}
			case 1:
				// 更新客户端状态
				message = map[string]interface{}{
					"type": "client_state_update",
					"data": map[string]interface{}{
						"page":      1,
						"page_size": 10,
						"filters": map[string]interface{}{
							"status": "",
							"search": fmt.Sprintf("test_%d", clientID),
						},
					},
				}
			case 2:
				// 请求设备列表
				message = map[string]interface{}{
					"type": "get_device_list",
					"data": nil,
				}
			case 3:
				// 请求设备详情
				message = map[string]interface{}{
					"type": "get_device_detail",
					"data": map[string]interface{}{
						"imsi": "test_imsi_" + fmt.Sprintf("%d", clientID),
					},
				}
			}
			
			if err := conn.WriteJSON(message); err != nil {
				log.Printf("客户端 %d: 发送消息失败: %v", clientID, err)
				*messageErrors++
				return
			}
			
			*totalMessagesSent++
			messageCount++
			
			if clientID == 0 && messageCount%50 == 0 {
				fmt.Printf("📤 客户端 %d 已发送 %d 条消息\n", clientID, messageCount)
			}
		}
	}
	
	fmt.Printf("✅ 客户端 %d 测试完成，发送了 %d 条消息\n", clientID, messageCount)
}
