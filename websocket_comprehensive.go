package main

import (
	"fmt"
	"math/rand"
	"net/url"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// ==================== 消息类型定义 ====================
const (
	// 客户端发送的消息类型
	MSG_GET_DEVICE_LIST     = "get_device_list"
	MSG_GET_CLIENT_STATES   = "get_client_states"
	MSG_CLIENT_STATE_UPDATE = "client_state_update"
	MSG_PING                = "ping"

	// 服务端发送的消息类型
	MSG_DEVICE_LIST_UPDATE   = "device_list_update"
	MSG_CLIENT_STATES_UPDATE = "client_states_update"
	MSG_PONG                 = "pong"
)

// ==================== 数据结构定义 ====================

// WebSocket消息结构
type WebSocketMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// 客户端状态更新数据
type ClientStateData struct {
	Page     int                    `json:"page"`
	PageSize int                    `json:"page_size"`
	Filters  map[string]interface{} `json:"filters"`
}

// 虚拟客户端
type VirtualClient struct {
	ID                string
	Conn              *websocket.Conn
	State             ClientStateData
	IsConnected       bool
	MessagesSent      int64
	MessagesReceived  int64
	Errors            []string
	StartTime         time.Time
	LastHeartbeat     time.Time
	ExpectedResponses map[string]bool // 期望收到的响应类型
	ReceivedResponses map[string]bool // 实际收到的响应类型
	mutex             sync.RWMutex
}

// 测试统计
type TestStatistics struct {
	TotalClients        int64
	SuccessfulConnects  int64
	FailedConnects      int64
	TotalDisconnects    int64
	TotalMessagesSent   int64
	TotalMessagesRecv   int64
	TotalErrors         int64
	ConnectionLeaks     int64
	UnexpectedBehaviors []string
	mutex               sync.RWMutex
}

// 连接数监控器
type ConnectionMonitor struct {
	ExpectedCount int64
	ActualCount   int64
	Anomalies     []string
	mutex         sync.RWMutex
}

// ==================== 全局变量 ====================
var (
	serverURL = "ws://localhost:8080/ws"
	stats     = &TestStatistics{}
	monitor   = &ConnectionMonitor{}
)

// ==================== 虚拟客户端实现 ====================

// 创建新的虚拟客户端
func NewVirtualClient(id string) *VirtualClient {
	return &VirtualClient{
		ID:       id,
		StartTime: time.Now(),
		State: ClientStateData{
			Page:     1,
			PageSize: 10,
			Filters: map[string]interface{}{
				"status": "all",
				"search": "",
			},
		},
		ExpectedResponses: make(map[string]bool),
		ReceivedResponses: make(map[string]bool),
	}
}

// 连接到WebSocket服务器
func (vc *VirtualClient) Connect() error {
	fmt.Printf("🔗 [%s] 开始连接到服务器...\n", vc.ID)
	atomic.AddInt64(&stats.TotalClients, 1)

	u, err := url.Parse(serverURL)
	if err != nil {
		return fmt.Errorf("解析URL失败: %v", err)
	}

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		atomic.AddInt64(&stats.FailedConnects, 1)
		atomic.AddInt64(&stats.TotalErrors, 1)
		return fmt.Errorf("连接失败: %v", err)
	}

	vc.Conn = conn
	vc.IsConnected = true
	atomic.AddInt64(&stats.SuccessfulConnects, 1)
	atomic.AddInt64(&monitor.ExpectedCount, 1)

	fmt.Printf("✅ [%s] 连接成功\n", vc.ID)

	// 启动消息接收goroutine
	go vc.messageReceiver()

	// 模拟前端连接后的初始化行为
	return vc.performInitialSetup()
}

// 执行初始化设置（模拟前端连接后的行为）
func (vc *VirtualClient) performInitialSetup() error {
	fmt.Printf("🚀 [%s] 执行初始化设置...\n", vc.ID)

	// 等待连接稳定
	time.Sleep(100 * time.Millisecond)

	// 1. 发送 get_device_list（模拟前端自动请求）
	vc.ExpectedResponses[MSG_DEVICE_LIST_UPDATE] = true
	if err := vc.sendMessage(MSG_GET_DEVICE_LIST, nil); err != nil {
		return fmt.Errorf("发送get_device_list失败: %v", err)
	}

	// 2. 发送 get_client_states（模拟前端自动请求）
	vc.ExpectedResponses[MSG_CLIENT_STATES_UPDATE] = true
	if err := vc.sendMessage(MSG_GET_CLIENT_STATES, nil); err != nil {
		return fmt.Errorf("发送get_client_states失败: %v", err)
	}

	// 3. 发送客户端状态更新（模拟前端初始化状态）
	if err := vc.updateClientState(); err != nil {
		return fmt.Errorf("更新客户端状态失败: %v", err)
	}

	fmt.Printf("✅ [%s] 初始化设置完成\n", vc.ID)
	return nil
}

// 发送消息
func (vc *VirtualClient) sendMessage(msgType string, data interface{}) error {
	if !vc.IsConnected {
		return fmt.Errorf("连接未建立")
	}

	message := WebSocketMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	if err := vc.Conn.WriteJSON(message); err != nil {
		atomic.AddInt64(&stats.TotalErrors, 1)
		vc.addError(fmt.Sprintf("发送消息失败: %v", err))
		return err
	}

	atomic.AddInt64(&vc.MessagesSent, 1)
	atomic.AddInt64(&stats.TotalMessagesSent, 1)
	fmt.Printf("📤 [%s] 发送消息: %s\n", vc.ID, msgType)
	return nil
}

// 更新客户端状态
func (vc *VirtualClient) updateClientState() error {
	return vc.sendMessage(MSG_CLIENT_STATE_UPDATE, vc.State)
}

// 消息接收器
func (vc *VirtualClient) messageReceiver() {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("❌ [%s] 消息接收器panic: %v\n", vc.ID, r)
		}
	}()

	for vc.IsConnected {
		var message WebSocketMessage
		err := vc.Conn.ReadJSON(&message)
		if err != nil {
			if vc.IsConnected {
				fmt.Printf("⚠️ [%s] 读取消息失败: %v\n", vc.ID, err)
				vc.addError(fmt.Sprintf("读取消息失败: %v", err))
			}
			break
		}

		atomic.AddInt64(&vc.MessagesReceived, 1)
		atomic.AddInt64(&stats.TotalMessagesRecv, 1)
		fmt.Printf("📥 [%s] 收到消息: %s\n", vc.ID, message.Type)

		vc.handleMessage(message)
	}
}

// 处理收到的消息
func (vc *VirtualClient) handleMessage(message WebSocketMessage) {
	vc.mutex.Lock()
	defer vc.mutex.Unlock()

	// 记录收到的响应
	vc.ReceivedResponses[message.Type] = true

	switch message.Type {
	case MSG_CLIENT_STATES_UPDATE:
		vc.handleClientStatesUpdate(message.Data)
	case MSG_DEVICE_LIST_UPDATE:
		vc.handleDeviceListUpdate(message.Data)
	case MSG_PONG:
		vc.LastHeartbeat = time.Now()
	default:
		fmt.Printf("🔍 [%s] 收到未知消息类型: %s\n", vc.ID, message.Type)
	}
}

// 处理客户端状态更新消息
func (vc *VirtualClient) handleClientStatesUpdate(data interface{}) {
	fmt.Printf("📊 [%s] 处理客户端状态更新\n", vc.ID)
	
	// 验证数据格式
	if dataMap, ok := data.(map[string]interface{}); ok {
		if clientCount, exists := dataMap["client_count"]; exists {
			atomic.StoreInt64(&monitor.ActualCount, int64(clientCount.(float64)))
			fmt.Printf("📈 [%s] 服务器报告客户端数量: %.0f\n", vc.ID, clientCount.(float64))
		}
	}
}

// 处理设备列表更新消息
func (vc *VirtualClient) handleDeviceListUpdate(data interface{}) {
	fmt.Printf("📋 [%s] 处理设备列表更新\n", vc.ID)
	// 这里可以添加设备数据验证逻辑
}

// 模拟用户操作
func (vc *VirtualClient) simulateUserActions() error {
	if !vc.IsConnected {
		return fmt.Errorf("连接未建立")
	}

	actions := []func() error{
		vc.simulatePageChange,
		vc.simulateSearch,
		vc.simulateStatusFilter,
		vc.simulateHeartbeat,
	}

	// 随机执行用户操作
	for i := 0; i < 3; i++ {
		action := actions[rand.Intn(len(actions))]
		if err := action(); err != nil {
			return err
		}
		time.Sleep(time.Duration(rand.Intn(500)+100) * time.Millisecond)
	}

	return nil
}

// 模拟翻页操作
func (vc *VirtualClient) simulatePageChange() error {
	vc.State.Page = rand.Intn(5) + 1
	fmt.Printf("📄 [%s] 模拟翻页到第%d页\n", vc.ID, vc.State.Page)
	return vc.updateClientState()
}

// 模拟搜索操作
func (vc *VirtualClient) simulateSearch() error {
	searches := []string{"", "test", "device", "123"}
	vc.State.Filters["search"] = searches[rand.Intn(len(searches))]
	fmt.Printf("🔍 [%s] 模拟搜索: %s\n", vc.ID, vc.State.Filters["search"])
	return vc.updateClientState()
}

// 模拟状态过滤
func (vc *VirtualClient) simulateStatusFilter() error {
	statuses := []string{"all", "online", "offline"}
	vc.State.Filters["status"] = statuses[rand.Intn(len(statuses))]
	fmt.Printf("🎯 [%s] 模拟状态过滤: %s\n", vc.ID, vc.State.Filters["status"])
	return vc.updateClientState()
}

// 模拟心跳
func (vc *VirtualClient) simulateHeartbeat() error {
	vc.ExpectedResponses[MSG_PONG] = true
	return vc.sendMessage(MSG_PING, nil)
}

// 断开连接
func (vc *VirtualClient) Disconnect() {
	if !vc.IsConnected {
		return
	}

	fmt.Printf("🔌 [%s] 主动断开连接\n", vc.ID)
	vc.IsConnected = false
	
	if vc.Conn != nil {
		vc.Conn.Close()
	}
	
	atomic.AddInt64(&stats.TotalDisconnects, 1)
	atomic.AddInt64(&monitor.ExpectedCount, -1)
}

// 异常断开（不发送Close帧）
func (vc *VirtualClient) ForceDisconnect() {
	if !vc.IsConnected {
		return
	}

	fmt.Printf("💥 [%s] 强制断开连接（模拟异常）\n", vc.ID)
	vc.IsConnected = false
	
	if vc.Conn != nil {
		// 直接关闭底层连接，不发送WebSocket Close帧
		if tcpConn := vc.Conn.UnderlyingConn(); tcpConn != nil {
			tcpConn.Close()
		}
	}
	
	atomic.AddInt64(&stats.TotalDisconnects, 1)
	atomic.AddInt64(&monitor.ExpectedCount, -1)
}

// 添加错误记录
func (vc *VirtualClient) addError(errMsg string) {
	vc.mutex.Lock()
	defer vc.mutex.Unlock()
	vc.Errors = append(vc.Errors, errMsg)
}

// 验证响应完整性
func (vc *VirtualClient) validateResponses() []string {
	vc.mutex.RLock()
	defer vc.mutex.RUnlock()
	
	var issues []string
	for expectedType := range vc.ExpectedResponses {
		if !vc.ReceivedResponses[expectedType] {
			issues = append(issues, fmt.Sprintf("未收到期望的响应: %s", expectedType))
		}
	}
	return issues
}

// 获取客户端统计信息
func (vc *VirtualClient) getStats() map[string]interface{} {
	vc.mutex.RLock()
	defer vc.mutex.RUnlock()

	return map[string]interface{}{
		"id":                vc.ID,
		"connected_duration": time.Since(vc.StartTime),
		"messages_sent":     vc.MessagesSent,
		"messages_received": vc.MessagesReceived,
		"errors_count":      len(vc.Errors),
		"errors":            vc.Errors,
		"validation_issues": vc.validateResponses(),
	}
}

// ==================== 测试场景实现 ====================

// 测试场景1：基础功能测试
func testBasicFunctionality() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📋 测试场景1：基础功能测试")
	fmt.Println(strings.Repeat("=", 60))

	client := NewVirtualClient("BASIC-001")

	// 连接
	if err := client.Connect(); err != nil {
		fmt.Printf("❌ 基础功能测试失败: %v\n", err)
		return
	}

	// 等待初始化完成
	time.Sleep(2 * time.Second)

	// 模拟用户操作
	if err := client.simulateUserActions(); err != nil {
		fmt.Printf("⚠️ 用户操作模拟失败: %v\n", err)
	}

	// 等待响应
	time.Sleep(3 * time.Second)

	// 验证结果
	clientStats := client.getStats()
	fmt.Printf("📊 客户端统计: %+v\n", clientStats)

	// 正常断开
	client.Disconnect()

	// 等待服务器处理断开
	time.Sleep(1 * time.Second)

	fmt.Println("✅ 基础功能测试完成")
}

// 测试场景2：连接数验证测试（重点）
func testConnectionCountAccuracy() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📊 测试场景2：连接数验证测试（重点关注）")
	fmt.Println(strings.Repeat("=", 60))

	var clients []*VirtualClient
	targetCount := 5

	// 逐个建立连接，验证连接数
	for i := 0; i < targetCount; i++ {
		client := NewVirtualClient(fmt.Sprintf("COUNT-%03d", i+1))

		if err := client.Connect(); err != nil {
			fmt.Printf("❌ 客户端 %s 连接失败: %v\n", client.ID, err)
			continue
		}

		clients = append(clients, client)

		// 等待服务器处理连接
		time.Sleep(500 * time.Millisecond)

		// 检查连接数
		expectedCount := int64(i + 1)
		actualCount := atomic.LoadInt64(&monitor.ActualCount)

		fmt.Printf("📈 连接进度: %d/%d, 期望连接数: %d, 实际连接数: %d\n",
			i+1, targetCount, expectedCount, actualCount)

		if actualCount != expectedCount {
			anomaly := fmt.Sprintf("连接数不匹配: 期望=%d, 实际=%d", expectedCount, actualCount)
			monitor.mutex.Lock()
			monitor.Anomalies = append(monitor.Anomalies, anomaly)
			monitor.mutex.Unlock()
			fmt.Printf("⚠️ %s\n", anomaly)
		}
	}

	fmt.Printf("🔗 所有客户端连接完成，总连接数: %d\n", len(clients))
	time.Sleep(2 * time.Second)

	// 逐个断开连接，验证连接数
	for i, client := range clients {
		client.Disconnect()

		// 等待服务器处理断开
		time.Sleep(500 * time.Millisecond)

		expectedCount := int64(len(clients) - i - 1)
		actualCount := atomic.LoadInt64(&monitor.ActualCount)

		fmt.Printf("📉 断开进度: %d/%d, 期望连接数: %d, 实际连接数: %d\n",
			i+1, len(clients), expectedCount, actualCount)

		if actualCount != expectedCount {
			anomaly := fmt.Sprintf("断开后连接数不匹配: 期望=%d, 实际=%d", expectedCount, actualCount)
			monitor.mutex.Lock()
			monitor.Anomalies = append(monitor.Anomalies, anomaly)
			monitor.mutex.Unlock()
			fmt.Printf("⚠️ %s\n", anomaly)
		}
	}

	fmt.Println("✅ 连接数验证测试完成")
}

// 测试场景3：频繁刷新测试（模拟用户快速刷新页面）
func testFrequentRefresh() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🔄 测试场景3：频繁刷新测试（模拟页面刷新）")
	fmt.Println(strings.Repeat("=", 60))

	refreshCount := 10

	for i := 0; i < refreshCount; i++ {
		client := NewVirtualClient(fmt.Sprintf("REFRESH-%03d", i+1))

		fmt.Printf("🔄 第 %d 次刷新模拟\n", i+1)

		// 快速连接
		if err := client.Connect(); err != nil {
			fmt.Printf("❌ 刷新连接失败: %v\n", err)
			continue
		}

		// 短暂使用（模拟页面加载）
		time.Sleep(200 * time.Millisecond)

		// 发送一些消息
		client.simulateUserActions()

		// 短暂等待
		time.Sleep(100 * time.Millisecond)

		// 快速断开（模拟页面刷新）
		client.Disconnect()

		// 检查连接数
		time.Sleep(200 * time.Millisecond)
		actualCount := atomic.LoadInt64(&monitor.ActualCount)

		if actualCount != 0 {
			anomaly := fmt.Sprintf("刷新 %d 后连接数异常: %d", i+1, actualCount)
			monitor.mutex.Lock()
			monitor.Anomalies = append(monitor.Anomalies, anomaly)
			monitor.mutex.Unlock()
			fmt.Printf("⚠️ %s\n", anomaly)
		} else {
			fmt.Printf("✅ 刷新 %d 后连接数正常: %d\n", i+1, actualCount)
		}

		// 刷新间隔
		time.Sleep(50 * time.Millisecond)
	}

	fmt.Println("✅ 频繁刷新测试完成")
}

// 测试场景4：并发连接测试
func testConcurrentConnections() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🚀 测试场景4：并发连接测试")
	fmt.Println(strings.Repeat("=", 60))

	concurrentCount := 8
	var wg sync.WaitGroup
	var clients []*VirtualClient
	var clientsMutex sync.Mutex

	// 并发建立连接
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			client := NewVirtualClient(fmt.Sprintf("CONCURRENT-%03d", id+1))

			if err := client.Connect(); err != nil {
				fmt.Printf("❌ 并发客户端 %s 连接失败: %v\n", client.ID, err)
				return
			}

			clientsMutex.Lock()
			clients = append(clients, client)
			clientsMutex.Unlock()

			// 模拟并发操作
			for j := 0; j < 3; j++ {
				client.simulateUserActions()
				time.Sleep(time.Duration(rand.Intn(500)+100) * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()
	fmt.Printf("🔗 并发连接完成，成功连接数: %d\n", len(clients))

	// 等待所有操作完成
	time.Sleep(3 * time.Second)

	// 检查连接数
	actualCount := atomic.LoadInt64(&monitor.ActualCount)
	expectedCount := int64(len(clients))

	if actualCount != expectedCount {
		anomaly := fmt.Sprintf("并发连接数不匹配: 期望=%d, 实际=%d", expectedCount, actualCount)
		monitor.mutex.Lock()
		monitor.Anomalies = append(monitor.Anomalies, anomaly)
		monitor.mutex.Unlock()
		fmt.Printf("⚠️ %s\n", anomaly)
	} else {
		fmt.Printf("✅ 并发连接数正确: %d\n", actualCount)
	}

	// 并发断开
	for _, client := range clients {
		wg.Add(1)
		go func(c *VirtualClient) {
			defer wg.Done()
			c.Disconnect()
		}(client)
	}

	wg.Wait()
	time.Sleep(2 * time.Second)

	fmt.Println("✅ 并发连接测试完成")
}

// 测试场景5：异常断开测试
func testAbnormalDisconnections() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("💥 测试场景5：异常断开测试")
	fmt.Println(strings.Repeat("=", 60))

	abnormalCount := 3
	var clients []*VirtualClient

	// 建立连接
	for i := 0; i < abnormalCount; i++ {
		client := NewVirtualClient(fmt.Sprintf("ABNORMAL-%03d", i+1))

		if err := client.Connect(); err != nil {
			fmt.Printf("❌ 异常测试客户端 %s 连接失败: %v\n", client.ID, err)
			continue
		}

		clients = append(clients, client)
		time.Sleep(200 * time.Millisecond)
	}

	fmt.Printf("🔗 异常测试客户端连接完成: %d\n", len(clients))
	time.Sleep(1 * time.Second)

	// 模拟异常断开
	for i, client := range clients {
		fmt.Printf("💥 模拟客户端 %s 异常断开\n", client.ID)
		client.ForceDisconnect()

		// 等待服务器检测到异常断开
		time.Sleep(1 * time.Second)

		expectedCount := int64(len(clients) - i - 1)
		actualCount := atomic.LoadInt64(&monitor.ActualCount)

		fmt.Printf("📊 异常断开后连接数: 期望=%d, 实际=%d\n", expectedCount, actualCount)
	}

	// 等待服务器清理异常连接
	time.Sleep(5 * time.Second)

	finalCount := atomic.LoadInt64(&monitor.ActualCount)
	if finalCount != 0 {
		anomaly := fmt.Sprintf("异常断开后连接数未清零: %d", finalCount)
		monitor.mutex.Lock()
		monitor.Anomalies = append(monitor.Anomalies, anomaly)
		monitor.mutex.Unlock()
		fmt.Printf("⚠️ %s\n", anomaly)
	} else {
		fmt.Printf("✅ 异常断开后连接数正确清零\n")
	}

	fmt.Println("✅ 异常断开测试完成")
}

// 测试场景6：压力测试
func testHighLoad() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🔥 测试场景6：压力测试")
	fmt.Println(strings.Repeat("=", 60))

	loadClients := 15
	var wg sync.WaitGroup
	var clients []*VirtualClient
	var clientsMutex sync.Mutex

	// 高负载连接
	for i := 0; i < loadClients; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			client := NewVirtualClient(fmt.Sprintf("LOAD-%03d", id+1))

			if err := client.Connect(); err != nil {
				fmt.Printf("❌ 压力测试客户端 %s 连接失败: %v\n", client.ID, err)
				return
			}

			clientsMutex.Lock()
			clients = append(clients, client)
			clientsMutex.Unlock()

			// 高频操作
			for j := 0; j < 10; j++ {
				client.simulateUserActions()
				time.Sleep(time.Duration(rand.Intn(100)+50) * time.Millisecond)
			}
		}(i)

		// 错开连接时间
		time.Sleep(20 * time.Millisecond)
	}

	wg.Wait()
	fmt.Printf("🔗 压力测试连接完成: %d\n", len(clients))

	// 持续高频操作
	fmt.Println("🔥 开始高频操作...")
	for i := 0; i < 5; i++ {
		for _, client := range clients {
			wg.Add(1)
			go func(c *VirtualClient) {
				defer wg.Done()
				c.simulateUserActions()
			}(client)
		}
		wg.Wait()
		time.Sleep(100 * time.Millisecond)
	}

	// 检查连接稳定性
	actualCount := atomic.LoadInt64(&monitor.ActualCount)
	expectedCount := int64(len(clients))

	if actualCount != expectedCount {
		anomaly := fmt.Sprintf("压力测试后连接数异常: 期望=%d, 实际=%d", expectedCount, actualCount)
		monitor.mutex.Lock()
		monitor.Anomalies = append(monitor.Anomalies, anomaly)
		monitor.mutex.Unlock()
		fmt.Printf("⚠️ %s\n", anomaly)
	} else {
		fmt.Printf("✅ 压力测试连接数稳定: %d\n", actualCount)
	}

	// 清理连接
	for _, client := range clients {
		client.Disconnect()
	}

	time.Sleep(3 * time.Second)
	fmt.Println("✅ 压力测试完成")
}

// ==================== 主函数和报告生成 ====================

func main() {
	fmt.Println("🚀 WebSocket综合压力测试开始...")
	fmt.Println("测试目标: 验证WebSocket服务的稳定性、连接数准确性和并发处理能力")

	startTime := time.Now()

	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())

	// 执行所有测试场景
	testBasicFunctionality()
	time.Sleep(2 * time.Second)

	testConnectionCountAccuracy()
	time.Sleep(2 * time.Second)

	testFrequentRefresh()
	time.Sleep(2 * time.Second)

	testConcurrentConnections()
	time.Sleep(2 * time.Second)

	testAbnormalDisconnections()
	time.Sleep(2 * time.Second)

	testHighLoad()
	time.Sleep(2 * time.Second)

	// 生成最终报告
	generateFinalReport(startTime)
}

// 生成最终测试报告
func generateFinalReport(startTime time.Time) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📊 WebSocket综合压力测试报告")
	fmt.Println(strings.Repeat("=", 80))

	duration := time.Since(startTime)

	fmt.Printf("⏱️  测试总耗时: %v\n", duration)
	fmt.Printf("🔗 总连接尝试: %d\n", atomic.LoadInt64(&stats.TotalClients))
	fmt.Printf("✅ 成功连接数: %d\n", atomic.LoadInt64(&stats.SuccessfulConnects))
	fmt.Printf("❌ 失败连接数: %d\n", atomic.LoadInt64(&stats.FailedConnects))
	fmt.Printf("🔌 总断开数: %d\n", atomic.LoadInt64(&stats.TotalDisconnects))
	fmt.Printf("📤 总发送消息: %d\n", atomic.LoadInt64(&stats.TotalMessagesSent))
	fmt.Printf("📥 总接收消息: %d\n", atomic.LoadInt64(&stats.TotalMessagesRecv))
	fmt.Printf("⚠️  总错误数: %d\n", atomic.LoadInt64(&stats.TotalErrors))

	// 连接数准确性报告
	fmt.Println("\n📊 连接数准确性分析:")
	monitor.mutex.RLock()
	if len(monitor.Anomalies) == 0 {
		fmt.Println("✅ 连接数始终准确，未发现异常")
	} else {
		fmt.Printf("⚠️  发现 %d 个连接数异常:\n", len(monitor.Anomalies))
		for i, anomaly := range monitor.Anomalies {
			fmt.Printf("   %d. %s\n", i+1, anomaly)
		}
	}
	monitor.mutex.RUnlock()

	// 计算成功率
	totalAttempts := atomic.LoadInt64(&stats.TotalClients)
	successfulConnects := atomic.LoadInt64(&stats.SuccessfulConnects)
	successRate := float64(successfulConnects) / float64(totalAttempts) * 100

	fmt.Printf("\n📈 连接成功率: %.2f%%\n", successRate)

	// 最终评估
	fmt.Println("\n🎯 测试结果评估:")
	if len(monitor.Anomalies) == 0 && atomic.LoadInt64(&stats.TotalErrors) == 0 && successRate >= 95.0 {
		fmt.Println("🎉 测试完美通过！WebSocket服务运行稳定，连接数管理准确。")
	} else if len(monitor.Anomalies) > 0 {
		fmt.Println("⚠️  发现连接数管理问题，需要进一步调查服务器日志。")
	} else if atomic.LoadInt64(&stats.TotalErrors) > 0 {
		fmt.Println("⚠️  发现通信错误，建议检查网络和服务器稳定性。")
	} else {
		fmt.Println("⚠️  连接成功率偏低，建议检查服务器负载能力。")
	}

	fmt.Println("\n💡 建议检查服务器日志中的以下内容:")
	fmt.Println("   - AddClient 和 RemoveClient 的调用次数是否匹配")
	fmt.Println("   - 是否存在死锁或并发写入错误")
	fmt.Println("   - 异步操作的执行顺序是否正确")
	fmt.Println("   - 连接清理机制是否及时有效")

	fmt.Println("\n" + strings.Repeat("=", 80))
}
