#!/usr/bin/env python3
"""
查询TDengine数据库中指定设备的任务步骤数据
提取每个step值首次出现时的JSON数据，按时间顺序排序
"""

import requests
import json
import base64
from datetime import datetime
from collections import OrderedDict

# TDengine配置
TDENGINE_HOST = "************"
TDENGINE_PORT = "6041"
TDENGINE_USER = "root"
TDENGINE_PASSWORD = "Pixm2022"
TDENGINE_DATABASE = "pixmoving"

def query_tdengine(sql):
    """执行TDengine查询"""
    url = f"http://{TDENGINE_HOST}:{TDENGINE_PORT}/rest/sql/{TDENGINE_DATABASE}"
    
    # 构建认证头
    auth_string = f"{TDENGINE_USER}:{TDENGINE_PASSWORD}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        response = requests.post(url, data=sql, headers=headers, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"查询失败: {e}")
        return None

def parse_task_data(imsi, task_id, date):
    """查询并解析任务数据"""
    sql = f"""
    SELECT ts, latest_task 
    FROM device_heartbeat_{imsi} 
    WHERE latest_task LIKE '%"taskId":{task_id}%' 
    AND ts >= '{date} 00:00:00' 
    AND ts < '{date} 23:59:59'
    ORDER BY ts
    """
    
    print(f"执行查询: {sql}")
    result = query_tdengine(sql)
    
    if not result or result.get('code') != 0:
        print(f"查询失败: {result}")
        return None
    
    data = result.get('data', [])
    print(f"查询到 {len(data)} 条记录")
    
    # 用于存储每个step首次出现的数据
    step_first_occurrence = OrderedDict()
    
    for row in data:
        timestamp = row[0]
        latest_task_str = row[1]
        
        try:
            # 解析JSON数据
            task_data = json.loads(latest_task_str)
            step = task_data.get('step', None)
            
            if step is not None and step not in step_first_occurrence:
                # 记录该step首次出现的数据
                step_first_occurrence[step] = {
                    'timestamp': timestamp,
                    'step': step,
                    'task_data': task_data
                }
                print(f"发现新的step值: {step} 在时间: {timestamp}")
        
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}, 数据: {latest_task_str[:100]}...")
            continue
    
    return step_first_occurrence

def format_output(step_data):
    """格式化输出结果"""
    if not step_data:
        print("没有找到任何数据")
        return
    
    print("\n" + "="*80)
    print("任务步骤分析结果")
    print("="*80)
    
    for step, info in step_data.items():
        timestamp = info['timestamp']
        task_data = info['task_data']
        
        # 转换时间戳为可读格式
        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        readable_time = dt.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n【Step {step}】首次出现时间: {readable_time}")
        print("-" * 60)
        
        # 提取关键信息
        key_info = {
            'taskId': task_data.get('taskId'),
            'step': task_data.get('step'),
            'progress': task_data.get('progress'),
            'taskDistance': task_data.get('taskDistance'),
            'targetDistance': task_data.get('targetDistance'),
            'leftTime': task_data.get('leftTime'),
            'name': task_data.get('name'),
            'currentStation': task_data.get('currentStation', {}).get('name'),
            'targetStation': task_data.get('targetStation', {}).get('name'),
            'routeId': task_data.get('routeId')
        }
        
        print("关键信息:")
        for key, value in key_info.items():
            if value is not None:
                print(f"  {key}: {value}")
        
        print("\n完整JSON数据:")
        print(json.dumps(task_data, ensure_ascii=False, indent=2))
        print("-" * 60)

def main():
    """主函数"""
    # 查询参数
    imsi = "robobusrs026"
    task_id = 135
    date = "2025-08-01"
    
    print(f"查询参数:")
    print(f"  IMSI: {imsi}")
    print(f"  任务ID: {task_id}")
    print(f"  日期: {date}")
    print(f"  TDengine服务器: {TDENGINE_HOST}:{TDENGINE_PORT}")
    print()
    
    # 执行查询和分析
    step_data = parse_task_data(imsi, task_id, date)
    
    # 输出结果
    format_output(step_data)
    
    # 生成统计信息
    if step_data:
        print(f"\n统计信息:")
        print(f"  发现的不同step值: {list(step_data.keys())}")
        print(f"  step值总数: {len(step_data)}")
        
        # 按时间顺序显示step变化
        print(f"\nStep变化时间线:")
        for step, info in step_data.items():
            dt = datetime.fromisoformat(info['timestamp'].replace('Z', '+00:00'))
            readable_time = dt.strftime('%H:%M:%S')
            progress = info['task_data'].get('progress', 'N/A')
            print(f"  {readable_time} -> Step {step} (进度: {progress}%)")

if __name__ == "__main__":
    main()
