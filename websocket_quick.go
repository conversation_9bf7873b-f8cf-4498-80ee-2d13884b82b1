package main

import (
	"fmt"
	"net/url"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// 简化的虚拟客户端
type QuickClient struct {
	ID   string
	Conn *websocket.Conn
}

// 全局统计
var (
	serverURL     = "ws://localhost:8080/ws"
	successCount  int64
	failCount     int64
	disconnectCount int64
)

// 创建客户端并连接
func createAndConnect(id string) (*QuickClient, error) {
	fmt.Printf("🔗 [%s] 开始连接...\n", id)
	
	u, err := url.Parse(serverURL)
	if err != nil {
		return nil, err
	}

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		atomic.AddInt64(&failCount, 1)
		return nil, err
	}

	atomic.AddInt64(&successCount, 1)
	fmt.Printf("✅ [%s] 连接成功\n", id)

	client := &QuickClient{
		ID:   id,
		Conn: conn,
	}

	// 发送初始消息（模拟前端行为）
	client.sendInitialMessages()

	return client, nil
}

// 发送初始消息
func (c *QuickClient) sendInitialMessages() {
	// 发送 get_client_states
	msg1 := map[string]interface{}{
		"type": "get_client_states",
		"data": nil,
	}
	c.Conn.WriteJSON(msg1)

	// 发送 client_state_update
	msg2 := map[string]interface{}{
		"type": "client_state_update",
		"data": map[string]interface{}{
			"page":      1,
			"page_size": 10,
			"filters": map[string]interface{}{
				"status": "all",
				"search": "",
			},
		},
	}
	c.Conn.WriteJSON(msg2)

	fmt.Printf("📤 [%s] 发送初始消息完成\n", c.ID)
}

// 断开连接
func (c *QuickClient) disconnect() {
	if c.Conn != nil {
		c.Conn.Close()
		atomic.AddInt64(&disconnectCount, 1)
		fmt.Printf("🔌 [%s] 已断开连接\n", c.ID)
	}
}

// 测试连接数准确性
func testConnectionAccuracy() {
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println("📊 连接数准确性测试")
	fmt.Println(strings.Repeat("=", 60))

	var clients []*QuickClient
	targetCount := 5

	// 逐个建立连接
	for i := 0; i < targetCount; i++ {
		client, err := createAndConnect(fmt.Sprintf("TEST-%03d", i+1))
		if err != nil {
			fmt.Printf("❌ 连接失败: %v\n", err)
			continue
		}
		
		clients = append(clients, client)
		
		// 等待服务器处理
		time.Sleep(1 * time.Second)
		
		fmt.Printf("📈 当前应有 %d 个连接\n", len(clients))
	}

	fmt.Printf("🔗 所有连接建立完成，总数: %d\n", len(clients))
	
	// 等待一段时间观察
	time.Sleep(5 * time.Second)

	// 逐个断开连接
	for i, client := range clients {
		client.disconnect()
		
		// 等待服务器处理断开
		time.Sleep(1 * time.Second)
		
		remaining := len(clients) - i - 1
		fmt.Printf("📉 断开第 %d 个连接，应剩余 %d 个连接\n", i+1, remaining)
	}

	// 最终等待
	time.Sleep(3 * time.Second)
	
	fmt.Printf("✅ 连接数测试完成\n")
	fmt.Printf("📊 统计: 成功=%d, 失败=%d, 断开=%d\n", 
		atomic.LoadInt64(&successCount), 
		atomic.LoadInt64(&failCount), 
		atomic.LoadInt64(&disconnectCount))
}

// 测试并发连接
func testConcurrentConnections() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🚀 并发连接测试")
	fmt.Println(strings.Repeat("=", 60))

	concurrentCount := 10
	var wg sync.WaitGroup
	var clients []*QuickClient
	var clientsMutex sync.Mutex

	// 并发建立连接
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			client, err := createAndConnect(fmt.Sprintf("CONCURRENT-%03d", id+1))
			if err != nil {
				fmt.Printf("❌ 并发连接失败: %v\n", err)
				return
			}
			
			clientsMutex.Lock()
			clients = append(clients, client)
			clientsMutex.Unlock()
		}(i)
	}

	wg.Wait()
	fmt.Printf("🔗 并发连接完成，成功连接数: %d\n", len(clients))
	
	// 等待观察
	time.Sleep(5 * time.Second)

	// 并发断开
	for _, client := range clients {
		wg.Add(1)
		go func(c *QuickClient) {
			defer wg.Done()
			c.disconnect()
		}(client)
	}
	
	wg.Wait()
	time.Sleep(3 * time.Second)
	
	fmt.Printf("✅ 并发连接测试完成\n")
}

// 测试频繁刷新
func testFrequentRefresh() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🔄 频繁刷新测试")
	fmt.Println(strings.Repeat("=", 60))

	refreshCount := 8
	
	for i := 0; i < refreshCount; i++ {
		fmt.Printf("🔄 第 %d 次刷新\n", i+1)
		
		// 快速连接
		client, err := createAndConnect(fmt.Sprintf("REFRESH-%03d", i+1))
		if err != nil {
			fmt.Printf("❌ 刷新连接失败: %v\n", err)
			continue
		}
		
		// 短暂使用
		time.Sleep(200 * time.Millisecond)
		
		// 快速断开
		client.disconnect()
		
		// 等待服务器处理
		time.Sleep(300 * time.Millisecond)
		
		fmt.Printf("✅ 第 %d 次刷新完成\n", i+1)
	}

	fmt.Printf("✅ 频繁刷新测试完成\n")
}

func main() {
	fmt.Println("🚀 WebSocket快速测试开始...")
	
	startTime := time.Now()
	
	// 执行测试
	testConnectionAccuracy()
	time.Sleep(2 * time.Second)
	
	testConcurrentConnections()
	time.Sleep(2 * time.Second)
	
	testFrequentRefresh()
	time.Sleep(2 * time.Second)
	
	// 最终报告
	duration := time.Since(startTime)
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📊 测试报告")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("⏱️  总耗时: %v\n", duration)
	fmt.Printf("✅ 成功连接: %d\n", atomic.LoadInt64(&successCount))
	fmt.Printf("❌ 失败连接: %d\n", atomic.LoadInt64(&failCount))
	fmt.Printf("🔌 总断开数: %d\n", atomic.LoadInt64(&disconnectCount))
	
	successRate := float64(atomic.LoadInt64(&successCount)) / float64(atomic.LoadInt64(&successCount)+atomic.LoadInt64(&failCount)) * 100
	fmt.Printf("📈 连接成功率: %.2f%%\n", successRate)
	
	if atomic.LoadInt64(&failCount) == 0 {
		fmt.Println("🎉 所有连接测试通过！")
	} else {
		fmt.Println("⚠️  发现连接问题，请检查服务器日志")
	}
	
	fmt.Println("\n💡 请检查服务器日志中的连接数变化")
	fmt.Println(strings.Repeat("=", 60))
}
