package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"
)

// TDengine配置
const (
	TDengineHost     = "************"
	TDenginePort     = "6041"
	TDengineUser     = "root"
	TDenginePassword = "Pixm2022"
	TDengineDatabase = "pixmoving"
)

// TDengine响应结构
type TDengineResponse struct {
	Code       int           `json:"code"`
	ColumnMeta [][]any       `json:"column_meta"`
	Data       [][]any       `json:"data"`
	Rows       int           `json:"rows"`
}

// 任务数据结构
type TaskData struct {
	TaskID         int    `json:"taskId"`
	TaskDistance   int    `json:"taskDistance"`
	TargetStation  Station `json:"targetStation"`
	TargetDistance int    `json:"targetDistance"`
	Step           int    `json:"step"`
	StartTime      int    `json:"startTime"`
	RouteID        int    `json:"routeId"`
	Progress       int    `json:"progress"`
	Point          string `json:"point"`
	Other          string `json:"other"`
	NextPoint      string `json:"nextPoint"`
	Name           string `json:"name"`
	LeftTime       int    `json:"leftTime"`
	Desp           string `json:"desp"`
	CurrentStation Station `json:"currentStation"`
	ActionList     []any  `json:"actionList"`
}

type Station struct {
	Name string `json:"name"`
	ID   int    `json:"id"`
}

// 步骤数据
type StepData struct {
	Timestamp string   `json:"timestamp"`
	Step      int      `json:"step"`
	TaskData  TaskData `json:"task_data"`
}

// 查询TDengine数据库
func queryTDengine(sql string) (*TDengineResponse, error) {
	url := fmt.Sprintf("http://%s:%s/rest/sql/%s", TDengineHost, TDenginePort, TDengineDatabase)
	
	// 构建认证头
	auth := base64.StdEncoding.EncodeToString([]byte(TDengineUser + ":" + TDenginePassword))
	
	req, err := http.NewRequest("POST", url, strings.NewReader(sql))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	
	req.Header.Set("Authorization", "Basic "+auth)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	
	var result TDengineResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}
	
	if result.Code != 0 {
		return nil, fmt.Errorf("TDengine查询失败, code: %d", result.Code)
	}
	
	return &result, nil
}

// 解析任务数据
func parseTaskData(imsi string, taskID int, date string) (map[int]StepData, error) {
	sql := fmt.Sprintf(`
		SELECT ts, latest_task 
		FROM device_heartbeat_%s 
		WHERE latest_task LIKE '%%"taskId":%d%%' 
		AND ts >= '%s 00:00:00' 
		AND ts < '%s 23:59:59'
		ORDER BY ts
	`, imsi, taskID, date, date)
	
	fmt.Printf("执行查询: %s\n", sql)
	
	result, err := queryTDengine(sql)
	if err != nil {
		return nil, err
	}
	
	fmt.Printf("查询到 %d 条记录\n", result.Rows)
	
	// 用于存储每个step首次出现的数据
	stepFirstOccurrence := make(map[int]StepData)
	
	for _, row := range result.Data {
		if len(row) < 2 {
			continue
		}
		
		timestamp, ok := row[0].(string)
		if !ok {
			continue
		}
		
		latestTaskStr, ok := row[1].(string)
		if !ok {
			continue
		}
		
		// 解析JSON数据
		var taskData TaskData
		if err := json.Unmarshal([]byte(latestTaskStr), &taskData); err != nil {
			fmt.Printf("JSON解析失败: %v, 数据: %s...\n", err, latestTaskStr[:min(100, len(latestTaskStr))])
			continue
		}
		
		step := taskData.Step
		
		// 如果这个step还没有记录过，则记录
		if _, exists := stepFirstOccurrence[step]; !exists {
			stepFirstOccurrence[step] = StepData{
				Timestamp: timestamp,
				Step:      step,
				TaskData:  taskData,
			}
			fmt.Printf("发现新的step值: %d 在时间: %s\n", step, timestamp)
		}
	}
	
	return stepFirstOccurrence, nil
}

// 格式化输出结果
func formatOutput(stepData map[int]StepData) {
	if len(stepData) == 0 {
		fmt.Println("没有找到任何数据")
		return
	}
	
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("任务步骤分析结果")
	fmt.Println(strings.Repeat("=", 80))
	
	// 按step值排序
	var steps []int
	for step := range stepData {
		steps = append(steps, step)
	}
	sort.Ints(steps)
	
	for _, step := range steps {
		info := stepData[step]
		
		// 转换时间戳为可读格式
		timestamp := strings.Replace(info.Timestamp, "Z", "+00:00", 1)
		t, err := time.Parse("2006-01-02T15:04:05.000+00:00", timestamp)
		if err != nil {
			fmt.Printf("时间解析失败: %v\n", err)
			continue
		}
		readableTime := t.Format("2006-01-02 15:04:05")
		
		fmt.Printf("\n【Step %d】首次出现时间: %s\n", step, readableTime)
		fmt.Println(strings.Repeat("-", 80))
		
		// 提取关键信息
		taskData := info.TaskData
		fmt.Println("关键信息:")
		fmt.Printf("  %-15s: %d\n", "taskId", taskData.TaskID)
		fmt.Printf("  %-15s: %d\n", "step", taskData.Step)
		fmt.Printf("  %-15s: %d\n", "progress", taskData.Progress)
		fmt.Printf("  %-15s: %d\n", "taskDistance", taskData.TaskDistance)
		fmt.Printf("  %-15s: %d\n", "targetDistance", taskData.TargetDistance)
		fmt.Printf("  %-15s: %d\n", "leftTime", taskData.LeftTime)
		fmt.Printf("  %-15s: %s\n", "name", taskData.Name)
		fmt.Printf("  %-15s: %s\n", "currentStation", taskData.CurrentStation.Name)
		fmt.Printf("  %-15s: %s\n", "targetStation", taskData.TargetStation.Name)
		fmt.Printf("  %-15s: %d\n", "routeId", taskData.RouteID)
		
		fmt.Println("\n完整JSON数据:")
		jsonBytes, _ := json.MarshalIndent(taskData, "", "  ")
		fmt.Println(string(jsonBytes))
		fmt.Println(strings.Repeat("-", 60))
	}
}

// 生成统计信息
func generateStats(stepData map[int]StepData) {
	if len(stepData) == 0 {
		return
	}
	
	// 按step值排序
	var steps []int
	for step := range stepData {
		steps = append(steps, step)
	}
	sort.Ints(steps)
	
	fmt.Printf("\n统计信息:\n")
	fmt.Printf("  发现的不同step值: %v\n", steps)
	fmt.Printf("  step值总数: %d\n", len(steps))
	
	// 按时间顺序显示step变化
	fmt.Printf("\nStep变化时间线:\n")
	
	// 创建时间排序的切片
	type timeStep struct {
		time time.Time
		step int
		progress int
	}
	
	var timeSteps []timeStep
	for _, step := range steps {
		info := stepData[step]
		timestamp := strings.Replace(info.Timestamp, "Z", "+00:00", 1)
		t, err := time.Parse("2006-01-02T15:04:05.000+00:00", timestamp)
		if err != nil {
			continue
		}
		
		timeSteps = append(timeSteps, timeStep{
			time: t,
			step: step,
			progress: info.TaskData.Progress,
		})
	}
	
	// 按时间排序
	sort.Slice(timeSteps, func(i, j int) bool {
		return timeSteps[i].time.Before(timeSteps[j].time)
	})
	
	for _, ts := range timeSteps {
		readableTime := ts.time.Format("15:04:05")
		fmt.Printf("  %-8s -> Step %d (进度: %d%%)\n", readableTime, ts.step, ts.progress)
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func main() {
	// 查询参数
	imsi := "robobusrs026"
	taskID := 134
	date := "2025-08-01"
	
	fmt.Printf("查询参数:\n")
	fmt.Printf("  IMSI: %s\n", imsi)
	fmt.Printf("  任务ID: %d\n", taskID)
	fmt.Printf("  日期: %s\n", date)
	fmt.Printf("  TDengine服务器: %s:%s\n", TDengineHost, TDenginePort)
	fmt.Println()
	
	// 执行查询和分析
	stepData, err := parseTaskData(imsi, taskID, date)
	if err != nil {
		fmt.Printf("查询失败: %v\n", err)
		return
	}
	
	// 输出结果
	formatOutput(stepData)
	
	// 生成统计信息
	generateStats(stepData)
}
