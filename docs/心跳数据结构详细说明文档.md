# 心跳数据结构详细说明文档

## 📋 目录
- [1. 概述](#1-概述)
- [2. 数据结构总览](#2-数据结构总览)
- [3. 基础信息字段](#3-基础信息字段)
- [4. 车辆状态字段](#4-车辆状态字段)
- [5. 里程与动力字段](#5-里程与动力字段)
- [6. 电力系统字段](#6-电力系统字段)
- [7. 定位导航字段](#7-定位导航字段)
- [8. 控制系统字段](#8-控制系统字段)
- [9. 安全环境字段](#9-安全环境字段)
- [10. 任务信息字段](#10-任务信息字段)
- [11. 告警系统字段](#11-告警系统字段)
- [12. 饮品机器人专用字段](#12-饮品机器人专用字段)
- [13. 字段使用场景](#13-字段使用场景)

---

## 1. 概述

### 1.1 心跳数据作用
心跳数据是车载设备向服务端实时上报的状态信息，包含车辆的位置、状态、任务执行情况等关键信息。

### 1.2 上报频率
- **正常情况**: 每2秒上报一次
- **异常情况**: 根据事件触发立即上报

### 1.3 数据格式
- **协议**: TCP/JSON格式
- **命令码**: 0x0002 (心跳协议)
- **编码**: UTF-8

---

## 2. 数据结构总览

```json
{
    "cmd": 2,                    // 消息类型
    "uid": "213",                // 终端编号
    "vType": 0,                  // 车辆类型
    "acc": 1,                    // ACC状态
    "gear": 0,                   // 档位
    "tm": 190,                   // 总里程
    "lng": 113.91040802001953,   // GPS经度
    "lat": 22.487064361572266,   // GPS纬度
    "latestTask": {              // 最新任务信息
        "step": 0,               // 任务状态
        "taskId": 0,             // 任务ID
        "taskDistance": 360      // 任务总距离
    },
    // ... 其他字段
}
```

---

## 3. 基础信息字段

| 字段名 | 数据类型 | 单位 | 说明 | 可能值 |
|--------|----------|------|------|--------|
| **cmd** | U8 | - | 消息类型，固定值 | `2` (0x0002) |
| **uid** | String | - | 终端编号，设备唯一标识 | 如: "213", "8788897871011" |
| **vType** | U8 | - | 车辆类型 | `0`:RoboBus<br>`1`:安防小车<br>`2`:无人清扫车<br>`3`:物流车<br>`6`:饮品零售机器人 |

### 3.1 字段详解

#### vType (车辆类型)
```go
const (
    VehicleTypeRoboBus     = 0  // RoboBus公交车
    VehicleTypeSecurity    = 1  // 安防小车
    VehicleTypeCleaner     = 2  // 无人清扫车
    VehicleTypeLogistics   = 3  // 物流车
    VehicleTypeRetailBot   = 6  // 饮品零售机器人
)
```

---

## 4. 车辆状态字段

### 4.1 基础状态

| 字段名 | 数据类型 | 单位 | 说明 | 可能值 |
|--------|----------|------|------|--------|
| **acc** | S8 | - | ACC状态(钥匙状态) | `0`:OFF<br>`1`:ON<br>`-1`:未知 |
| **gear** | S8 | - | 档位状态 | `0`:P档<br>`2`:R档<br>`3`:N档<br>`4`:D档<br>`-1`:未知 |
| **door** | S8 | - | 车门开关状态 | `0`:关闭<br>`1`:打开<br>`-1`:未知 |
| **headLight** | S8 | - | 大灯开关状态 | `0`:关闭<br>`1`:打开<br>`-1`:未知 |
| **win** | S8 | - | 车窗开关状态 | `0`:关闭<br>`1`:打开<br>`-1`:未知 |

### 4.2 驾驶模式

| 字段名 | 数据类型 | 单位 | 说明 | 可能值 |
|--------|----------|------|------|--------|
| **mode** | U8 | - | 车辆驾驶模式 | `1`:遥控模式<br>`2`:自驾待命状态<br>`3`:自驾执行状态<br>`4`:远程驾驶状态<br>`5`:紧急停止状态<br>`-1`:未知 |

### 4.3 状态示例
```json
{
    "acc": 1,        // ACC开启
    "gear": 4,       // D档
    "door": 0,       // 车门关闭
    "headLight": 1,  // 大灯开启
    "mode": 3        // 自驾执行状态
}
```

---

## 5. 里程与动力字段

### 5.1 里程信息

| 字段名 | 数据类型 | 单位 | 说明 | 特殊值 |
|--------|----------|------|------|--------|
| **tm** | S32 | km | 总里程(累计行驶距离) | `-1`:未知 |
| **rm** | S16 | km | 当前电量可跑续航里程 | `-1`:未知 |

### 5.2 速度信息

| 字段名 | 数据类型 | 单位 | 说明 | 特殊值 |
|--------|----------|------|------|--------|
| **spd** | S16 | km/h | 当前车速 | `-1`:未知 |
| **spdL** | S16 | km/h | 限速设置 | `-1`:未知 |

### 5.3 使用场景
- **tm**: 用于计算任务执行期间的实际行驶里程
- **rm**: 用于电量管理和续航预警
- **spd**: 用于速度监控和超速告警
- **spdL**: 用于速度限制和安全管控

---

## 6. 电力系统字段

### 6.1 电池信息

| 字段名 | 数据类型 | 单位 | 说明 | 特殊值 |
|--------|----------|------|------|--------|
| **pL** | S8 | % | 剩余电量百分比 | `-1`:未知 |
| **pV** | S16 | 0.1V | 大电瓶电压 | `-1`:未知 |
| **pC** | S16 | 0.1A | 大电瓶电流 | `-1`:未知 |
| **bat** | S16 | 0.1V | 小电瓶电压 | `-1`:未知 |
| **pCh** | S8 | - | 充电状态 | `0`:未充电<br>`1`:充电中<br>`-1`:未知 |

### 6.2 电压电流计算
```go
// 实际电压计算 (单位: V)
actualVoltage := float64(pV) / 10.0

// 实际电流计算 (单位: A)  
actualCurrent := float64(pC) / 10.0
```

### 6.3 电量监控示例
```json
{
    "pL": 72,    // 电量72%
    "pV": 317,   // 大电瓶电压31.7V
    "pC": 9,     // 大电瓶电流0.9A
    "bat": 13,   // 小电瓶电压1.3V
    "pCh": 0     // 未充电
}
```

---

## 7. 定位导航字段

### 7.1 定位状态

| 字段名 | 数据类型 | 单位 | 说明 | 可能值 |
|--------|----------|------|------|--------|
| **adLocated** | Bool | - | 自动驾驶是否定位 | `true`:已定位<br>`false`:未定位 |
| **gpsLocated** | Bool | - | GPS是否定位 | `true`:已定位<br>`false`:未定位 |

### 7.2 位置信息

| 字段名 | 数据类型 | 单位 | 精度 | 说明 |
|--------|----------|------|------|------|
| **lng** | Float64 | 度 | 6位小数 | GPS经度 |
| **lat** | Float64 | 度 | 6位小数 | GPS纬度 |
| **alt** | Float32 | 米 | 1位小数 | GPS海拔 |
| **angle** | Float32 | 度 | 1位小数 | GPS航向角度 |

### 7.3 定位质量

| 字段名 | 数据类型 | 单位 | 说明 | 特殊值 |
|--------|----------|------|------|--------|
| **posQ** | Float32 | % | 自驾定位质量 | `-1`:未知 |
| **satCnt** | U8 | 个 | 有效GPS卫星数 | `-1`:未知 |
| **gpsStrength** | U8 | % | GPS定位强度百分比 | `-1`:未知 |

### 7.4 位置数据示例
```json
{
    "adLocated": true,
    "gpsLocated": true,
    "lng": 113.91040802001953,  // 东经113.910408度
    "lat": 22.487064361572266,  // 北纬22.487064度
    "alt": 0.7,                 // 海拔0.7米
    "angle": 130.91,            // 航向角130.91度
    "posQ": 85.5                // 定位质量85.5%
}
```

---

## 8. 控制系统字段

### 8.1 通讯状态

| 字段名 | 数据类型 | 说明 | 可能值 |
|--------|----------|------|--------|
| **ipcToCha** | Bool | 工控机与底盘通讯状态 | `true`:通讯中<br>`false`:未通讯 |

### 8.2 操控信息

| 字段名 | 数据类型 | 单位 | 精度 | 说明 | 特殊值 |
|--------|----------|------|------|------|--------|
| **ste** | Float32 | 0.1度 | 1位小数 | 方向盘转角 | `-1`:未知 |
| **brk** | Float32 | % | 2位小数 | 制动值 | `-1`:未知 |
| **thr** | Float32 | % | 2位小数 | 油门值 | `-1`:未知 |

### 8.3 转向模式

| 字段名 | 数据类型 | 说明 | 可能值 |
|--------|----------|------|--------|
| **headMode** | U8 | 当前转向模式 | `1`:前后异向模式<br>`2`:常规模式<br>`3`:前后同向模式 |

### 8.4 控制数据示例
```json
{
    "ipcToCha": false,  // 工控机与底盘未通讯
    "ste": 0.0,         // 方向盘转角0度
    "brk": 0.0,         // 制动值0%
    "thr": 0.0,         // 油门值0%
    "headMode": 1       // 前后异向模式
}
```

---

## 9. 安全环境字段

### 9.1 急停状态

| 字段名 | 数据类型 | 说明 | 可能值 |
|--------|----------|------|--------|
| **emgSta** | U8 | 急停状态反馈 | `0`:无<br>`1`:车身急停<br>`2`:遥控急停<br>`3`:遥控断开急停<br>`4`:自驾断开急停<br>`5`:远控急停<br>`6`:自驾急停<br>`7`:碰撞急停 |

### 9.2 安全设备

| 字段名 | 数据类型 | 说明 | 可能值 |
|--------|----------|------|--------|
| **seatbelt** | U8 | 安全带状态 | `0`:正常(所有乘客均系安全带)<br>`1`:异常(有乘客未系安全带)<br>`-1`:未知 |

### 9.3 座椅状态

| 字段名 | 数据类型 | 说明 | 编码规则 |
|--------|----------|------|----------|
| **seat** | U8[6] | 座位状态数组 | 高4位:安全带状态<br>低4位:座压状态<br>`0x10`:安全带已系、未检测到座压 |

### 9.4 环境监测

| 字段名 | 数据类型 | 单位 | 说明 | 特殊值 |
|--------|----------|------|------|--------|
| **inTemp** | U8 | 度 | 车内温度 | `-100`:未知 |
| **outTemp** | U8 | 度 | 车外温度 | `-100`:未知 |
| **smoke** | S32 | mV | 烟雾传感器电压值 | `-1`:未知 |
| **co2** | S32 | mV | 二氧化碳传感器电压值 | `-1`:未知 |
| **cm** | S32 | - | CM状态值 | `-1`:未知 |
| **airCon** | U8 | - | 空调开关状态 | `0`:关闭<br>`1`:开启<br>`-1`:未知 |

---

## 10. 任务信息字段

### 10.1 任务基础信息

| 字段名 | 数据类型 | 单位 | 说明 | 特殊值 |
|--------|----------|------|------|--------|
| **step** | S8 | - | 任务执行状态 | 见任务状态对照表 |
| **taskId** | U64 | - | 任务ID | `0`:无任务 |
| **routeId** | U64 | - | 路线ID | `0`:无路线 |
| **progress** | S8 | % | 任务完成百分比 | `-1`:未知 |
| **startTime** | U32 | 秒 | 任务开始时间(UNIX时间戳) | `0`:未开始 |

### 10.2 任务距离信息

| 字段名 | 数据类型 | 单位 | 说明 | 特殊值 |
|--------|----------|------|------|--------|
| **taskDistance** | U32 | 米 | 任务总距离(理论规划里程) | `0`:无距离信息 |
| **targetDistance** | U32 | 米 | 目标剩余距离 | `0`:已到站 |
| **leftTime** | S16 | 秒 | 到达下一站预计时间 | `-1`:未知 |

### 10.3 任务描述

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| **name** | String | 任务名称 |
| **desp** | String | 任务描述 |

### 10.4 站点信息

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| **currentStation.id** | U32 | 当前站点ID |
| **currentStation.name** | String | 当前站点名称 |
| **targetStation.id** | U32 | 目标站点ID |
| **targetStation.name** | String | 目标站点名称 |

### 10.5 任务状态对照表

| step值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 未开始 | 任务已下发但未开始执行 |
| 1 | 正在执行 | 任务正在执行中 |
| 2 | 已完成 | 任务执行完成 |
| 3 | 已取消 | 任务被用户取消 |
| 6 | 已暂停 | 任务暂停执行 |
| 8 | 已终止 | 任务被强制终止 |
| 9 | 无法完成 | 任务执行失败 |

### 10.6 任务数据示例
```json
{
    "latestTask": {
        "step": 1,
        "taskId": 12345,
        "routeId": 67890,
        "progress": 30,
        "startTime": 1703123456,
        "taskDistance": 360,
        "targetDistance": 252,
        "leftTime": 180,
        "name": "从A站到B站",
        "desp": "运输任务",
        "currentStation": {
            "id": 2,
            "name": "中心广场"
        },
        "targetStation": {
            "id": 1,
            "name": "ILOVESK"
        }
    }
}
```

---

## 11. 告警系统字段

### 11.1 告警计数

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| **alarmCnt1** | U32[4] | 告警数量数组1 |
| **alarmCnt2** | U32[8] | 告警数量数组2 |
| **alarmList** | Array | 告警列表 |

### 11.2 当前告警

| 字段名 | 数据类型 | 说明 | 可能值 |
|--------|----------|------|--------|
| **alarm.code** | U32 | 告警码值 | `0`:无告警 |
| **alarm.level** | U8 | 告警等级 | `0`:普通<br>`1`:警告<br>`2`:严重<br>`3`:致命 |
| **alarm.type** | U8 | 告警事件类型 | `0`:持久性告警产生<br>`1`:持久性告警恢复<br>`2`:瞬发性普通事件 |
| **alarm.msg** | String | 告警描述内容 | 空字符串表示无描述 |
| **alarm.ts** | U32 | 告警发生时间 | UNIX时间戳 |

### 11.3 系统状态

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| **botStatus** | U32[] | 整车状态数组 |
| **botErr** | U32[] | 整车故障状态数组 |
| **cErr** | U32[] | 底盘故障状态数组 |
| **autoErr** | U32[] | 自动驾驶故障状态数组 |
| **warning** | U32[] | 报警标志位数组 |

---

## 12. 饮品机器人专用字段

### 12.1 订单信息

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| **retailBot.billNo** | String | 当前订单号 |
| **retailBot.productId** | U32 | 当前制作的饮品型号ID |
| **retailBot.taskId** | U32 | 当前订单的第几杯 |

### 12.2 设备状态

| 字段名 | 数据类型 | 说明 | 可能值 |
|--------|----------|------|--------|
| **retailBot.deviceStatus** | U8 | 机器运行状态 | `0x00`:初始化中<br>`0x01`:空闲可接收任务<br>`0x02`:制作中<br>`0x03`:故障状态 |

### 12.3 制作进度

| 字段名 | 数据类型 | 单位 | 说明 |
|--------|----------|------|------|
| **retailBot.totalTime** | U32 | 秒 | 完成订单需要的总时间 |
| **retailBot.rTime** | U32 | 秒 | 订单制作完成还需时间 |
| **retailBot.actionIdx** | U8 | - | 当前执行步骤序号 |
| **retailBot.actionTime** | U32 | 秒 | 当前步骤总时间 |
| **retailBot.actionRTime** | U32 | 秒 | 当前步骤剩余时间 |

### 12.4 物料状态

| 字段名 | 数据类型 | 说明 | 编码规则 |
|--------|----------|------|----------|
| **retailBot.cupHolders** | U32 | 杯架状态 | 每位表示一个杯架是否有杯子 |
| **retailBot.liquid** | U32 | 液体桶状态 | 每位表示一个液体桶是否有液体 |
| **retailBot.solid** | U16 | 固态料筒状态 | 每位表示一个料筒是否有固体 |

---

## 13. 字段使用场景

### 13.1 任务统计导出字段映射

| 导出字段 | 心跳数据源 | 获取时机 | 计算方式 |
|----------|-----------|----------|----------|
| 运行任务 | `latestTask.taskId` | 任务执行期间 | 直接取值 |
| 理论规划里程(M) | `latestTask.taskDistance` | 任务开始(step=1) | 直接取值 |
| 实际执行时长(min) | 时间戳计算 | step=1到step=2 | (结束时间-开始时间)/60 |
| 最高运行速度 | `spd` | 任务执行期间 | MAX(spd) |
| 行车里程(M) | `tm` | 任务开始和结束 | (结束tm-开始tm)×1000 |
| 出发地点经纬度 | `lng`, `lat` | step=1时 | 直接取值 |
| 到达地点经纬度 | `lng`, `lat` | step=2时 | 直接取值 |

### 13.2 监控告警场景

| 监控项 | 相关字段 | 告警条件 | 处理建议 |
|--------|----------|----------|----------|
| 电量不足 | `pL` | pL < 20 | 提醒充电 |
| 定位异常 | `gpsLocated`, `adLocated` | false | 检查GPS设备 |
| 急停状态 | `emgSta` | emgSta > 0 | 立即处理 |
| 通讯异常 | `ipcToCha` | false | 检查通讯链路 |
| 温度异常 | `inTemp`, `outTemp` | 超出正常范围 | 环境调节 |

### 13.3 性能分析场景

| 分析维度 | 相关字段 | 计算方法 | 应用场景 |
|----------|----------|----------|----------|
| 能耗效率 | `pL`, `tm` | 里程/电量消耗 | 能耗优化 |
| 运行效率 | `spd`, `taskDistance` | 平均速度分析 | 路线优化 |
| 设备利用率 | `mode`, `step` | 工作时间占比 | 调度优化 |
| 定位精度 | `posQ`, `gpsStrength` | 定位质量统计 | 技术改进 |

---

## 📝 总结

本文档详细说明了心跳数据结构中的所有字段，包括：
- **基础信息**: 设备标识和车辆类型
- **状态监控**: 车辆各系统的实时状态
- **位置追踪**: GPS定位和导航信息
- **任务管理**: 任务执行状态和进度
- **安全监控**: 告警和安全设备状态
- **专用功能**: 饮品机器人特有字段

通过合理使用这些字段，可以实现全面的车辆监控、任务管理和数据分析功能。

---

## 14. 数据处理最佳实践

### 14.1 数据验证
```go
// 心跳数据验证示例
func ValidateHeartbeatData(data *HeartbeatData) error {
    // 检查必要字段
    if data.UID == "" {
        return errors.New("终端编号不能为空")
    }

    // 检查数据范围
    if data.PL < -1 || data.PL > 100 {
        return errors.New("电量百分比超出有效范围")
    }

    // 检查GPS坐标有效性
    if data.GpsLocated && (data.Lng == 0 || data.Lat == 0) {
        return errors.New("GPS已定位但坐标为0")
    }

    return nil
}
```

### 14.2 数据存储策略
- **实时数据**: 存储在Redis中，保留最近24小时
- **历史数据**: 按天分表存储在MySQL中
- **统计数据**: 定时聚合计算，存储统计结果

### 14.3 性能优化建议
1. **批量处理**: 心跳数据批量入库，减少数据库压力
2. **索引优化**: 为常用查询字段建立合适索引
3. **数据压缩**: 历史数据可考虑压缩存储
4. **缓存策略**: 热点数据使用Redis缓存

---

## 15. 常见问题解答

### 15.1 字段值为-1的处理
**问题**: 很多字段都有-1表示未知，如何处理？
**解答**:
- 在业务逻辑中忽略-1值
- 统计时排除-1的数据
- 界面显示时用"未知"或"--"表示

### 15.2 时间戳字段的处理
**问题**: startTime等时间戳字段为0表示什么？
**解答**:
- 0表示该事件尚未发生
- 需要区分"未开始"和"时间未知"
- 建议使用专门的状态字段判断

### 15.3 坐标精度问题
**问题**: GPS坐标精度如何保证？
**解答**:
- 经纬度保留6位小数，精度约0.1米
- 结合posQ字段判断定位质量
- 定位质量低时可考虑使用上一次有效位置

---

## 16. 版本更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2024-01-01 | 初始版本，包含基础心跳字段 |
| v1.1 | 2024-02-01 | 新增饮品机器人专用字段 |
| v1.2 | 2024-03-01 | 完善告警系统字段说明 |
| v1.3 | 2024-06-26 | 添加任务距离字段详细说明 |

---

## 17. 相关文档链接

- [任务管理系统分析文档](../项目分析报告-心跳数据处理与任务状态更新.md)
- [协议定义文档](../app/vars/protocol/protocol.go)
- [数据库设计文档](./数据库设计文档.md)

---

**文档维护**: 技术团队
**最后更新**: 2024-06-26
**联系方式**: <EMAIL>
