package main

import (
	"fmt"
	"net/url"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// 测试统计
type TestStats struct {
	TotalConnections    int64
	SuccessConnections  int64
	FailedConnections   int64
	TotalDisconnections int64
	MessagesSent        int64
	MessagesReceived    int64
	Errors              int64
}

// 连接信息
type ConnectionInfo struct {
	ID         int
	Conn       *websocket.Conn
	StartTime  time.Time
	EndTime    time.Time
	MessageCount int
	Errors     []string
}

func main() {
	fmt.Println("🚀 WebSocket压力测试开始...")
	
	serverURL := "ws://localhost:8080/ws"
	stats := &TestStats{}
	
	// 测试场景1：快速连接断开（模拟页面刷新）
	fmt.Println("\n📋 测试场景1：快速连接断开（模拟页面刷新）")
	testQuickConnectDisconnect(serverURL, stats, 20, 100*time.Millisecond)
	
	// 等待一段时间观察连接数变化
	time.Sleep(2 * time.Second)
	
	// 测试场景2：并发连接
	fmt.Println("\n📋 测试场景2：并发连接测试")
	testConcurrentConnections(serverURL, stats, 10, 5*time.Second)
	
	// 等待一段时间观察连接数变化
	time.Sleep(2 * time.Second)
	
	// 测试场景3：异常断开
	fmt.Println("\n📋 测试场景3：异常断开测试")
	testAbnormalDisconnections(serverURL, stats, 5)
	
	// 等待一段时间观察连接数变化
	time.Sleep(2 * time.Second)
	
	// 测试场景4：消息压力测试
	fmt.Println("\n📋 测试场景4：消息压力测试")
	testMessageStress(serverURL, stats, 3, 100, 50*time.Millisecond)
	
	// 输出最终统计
	printFinalStats(stats)
}

// 测试场景1：快速连接断开（模拟页面刷新）
func testQuickConnectDisconnect(serverURL string, stats *TestStats, count int, interval time.Duration) {
	fmt.Printf("   - 将进行 %d 次快速连接断开，间隔 %v\n", count, interval)
	
	for i := 0; i < count; i++ {
		go func(id int) {
			connInfo := &ConnectionInfo{
				ID:        id,
				StartTime: time.Now(),
			}
			
			fmt.Printf("🔗 [%d] 开始连接...\n", id)
			atomic.AddInt64(&stats.TotalConnections, 1)
			
			// 连接WebSocket
			u, _ := url.Parse(serverURL)
			conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
			if err != nil {
				fmt.Printf("❌ [%d] 连接失败: %v\n", id, err)
				atomic.AddInt64(&stats.FailedConnections, 1)
				atomic.AddInt64(&stats.Errors, 1)
				return
			}
			
			connInfo.Conn = conn
			atomic.AddInt64(&stats.SuccessConnections, 1)
			fmt.Printf("✅ [%d] 连接成功\n", id)
			
			// 发送一些消息
			messages := []map[string]interface{}{
				{"type": "get_client_states", "data": nil},
				{"type": "client_state_update", "data": map[string]interface{}{
					"page": 1, "page_size": 10, "filters": map[string]interface{}{},
				}},
			}
			
			for _, msg := range messages {
				if err := conn.WriteJSON(msg); err != nil {
					fmt.Printf("⚠️ [%d] 发送消息失败: %v\n", id, err)
					atomic.AddInt64(&stats.Errors, 1)
				} else {
					atomic.AddInt64(&stats.MessagesSent, 1)
					connInfo.MessageCount++
				}
			}
			
			// 短暂等待后断开
			time.Sleep(interval)
			
			fmt.Printf("🔌 [%d] 主动断开连接\n", id)
			conn.Close()
			atomic.AddInt64(&stats.TotalDisconnections, 1)
			
			connInfo.EndTime = time.Now()
			duration := connInfo.EndTime.Sub(connInfo.StartTime)
			fmt.Printf("📊 [%d] 连接持续时间: %v, 发送消息: %d\n", id, duration, connInfo.MessageCount)
		}(i)
		
		// 错开连接时间
		time.Sleep(10 * time.Millisecond)
	}
	
	// 等待所有连接完成
	time.Sleep(time.Duration(count) * interval + 2*time.Second)
}

// 测试场景2：并发连接
func testConcurrentConnections(serverURL string, stats *TestStats, count int, duration time.Duration) {
	fmt.Printf("   - 将同时建立 %d 个连接，持续 %v\n", count, duration)
	
	var wg sync.WaitGroup
	
	for i := 0; i < count; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			fmt.Printf("🔗 [并发-%d] 开始连接...\n", id)
			atomic.AddInt64(&stats.TotalConnections, 1)
			
			u, _ := url.Parse(serverURL)
			conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
			if err != nil {
				fmt.Printf("❌ [并发-%d] 连接失败: %v\n", id, err)
				atomic.AddInt64(&stats.FailedConnections, 1)
				atomic.AddInt64(&stats.Errors, 1)
				return
			}
			
			atomic.AddInt64(&stats.SuccessConnections, 1)
			fmt.Printf("✅ [并发-%d] 连接成功\n", id)
			
			// 启动消息接收goroutine
			go func() {
				for {
					_, _, err := conn.ReadMessage()
					if err != nil {
						return
					}
					atomic.AddInt64(&stats.MessagesReceived, 1)
				}
			}()
			
			// 定期发送消息
			ticker := time.NewTicker(1 * time.Second)
			defer ticker.Stop()
			
			endTime := time.Now().Add(duration)
			for time.Now().Before(endTime) {
				select {
				case <-ticker.C:
					msg := map[string]interface{}{
						"type": "get_client_states",
						"data": nil,
					}
					if err := conn.WriteJSON(msg); err != nil {
						fmt.Printf("⚠️ [并发-%d] 发送消息失败: %v\n", id, err)
						atomic.AddInt64(&stats.Errors, 1)
					} else {
						atomic.AddInt64(&stats.MessagesSent, 1)
					}
				}
			}
			
			fmt.Printf("🔌 [并发-%d] 连接结束\n", id)
			conn.Close()
			atomic.AddInt64(&stats.TotalDisconnections, 1)
		}(i)
	}
	
	wg.Wait()
}

// 测试场景3：异常断开
func testAbnormalDisconnections(serverURL string, stats *TestStats, count int) {
	fmt.Printf("   - 将测试 %d 个异常断开场景\n", count)
	
	for i := 0; i < count; i++ {
		go func(id int) {
			fmt.Printf("🔗 [异常-%d] 开始连接...\n", id)
			atomic.AddInt64(&stats.TotalConnections, 1)
			
			u, _ := url.Parse(serverURL)
			conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
			if err != nil {
				fmt.Printf("❌ [异常-%d] 连接失败: %v\n", id, err)
				atomic.AddInt64(&stats.FailedConnections, 1)
				atomic.AddInt64(&stats.Errors, 1)
				return
			}
			
			atomic.AddInt64(&stats.SuccessConnections, 1)
			fmt.Printf("✅ [异常-%d] 连接成功\n", id)
			
			// 发送一些消息
			for j := 0; j < 3; j++ {
				msg := map[string]interface{}{
					"type": "get_device_list",
					"data": nil,
				}
				if err := conn.WriteJSON(msg); err != nil {
					break
				}
				atomic.AddInt64(&stats.MessagesSent, 1)
				time.Sleep(100 * time.Millisecond)
			}
			
			// 模拟异常断开（不发送Close帧）
			fmt.Printf("💥 [异常-%d] 模拟异常断开（直接关闭底层连接）\n", id)
			if tcpConn := conn.UnderlyingConn(); tcpConn != nil {
				tcpConn.Close() // 直接关闭TCP连接，不发送WebSocket Close帧
			}
			atomic.AddInt64(&stats.TotalDisconnections, 1)
		}(i)
		
		time.Sleep(200 * time.Millisecond)
	}
	
	time.Sleep(3 * time.Second) // 等待异常断开被检测到
}

// 测试场景4：消息压力测试
func testMessageStress(serverURL string, stats *TestStats, connCount int, msgPerConn int, msgInterval time.Duration) {
	fmt.Printf("   - %d 个连接，每个发送 %d 条消息，间隔 %v\n", connCount, msgPerConn, msgInterval)
	
	var wg sync.WaitGroup
	
	for i := 0; i < connCount; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			fmt.Printf("🔗 [压力-%d] 开始连接...\n", id)
			atomic.AddInt64(&stats.TotalConnections, 1)
			
			u, _ := url.Parse(serverURL)
			conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
			if err != nil {
				fmt.Printf("❌ [压力-%d] 连接失败: %v\n", id, err)
				atomic.AddInt64(&stats.FailedConnections, 1)
				atomic.AddInt64(&stats.Errors, 1)
				return
			}
			
			atomic.AddInt64(&stats.SuccessConnections, 1)
			fmt.Printf("✅ [压力-%d] 连接成功，开始发送 %d 条消息\n", id, msgPerConn)
			
			// 发送大量消息
			for j := 0; j < msgPerConn; j++ {
				msgType := []string{"get_client_states", "client_state_update", "get_device_list"}[j%3]
				msg := map[string]interface{}{
					"type": msgType,
					"data": map[string]interface{}{
						"test_id": id,
						"msg_id":  j,
					},
				}
				
				if err := conn.WriteJSON(msg); err != nil {
					fmt.Printf("⚠️ [压力-%d] 消息 %d 发送失败: %v\n", id, j, err)
					atomic.AddInt64(&stats.Errors, 1)
					break
				}
				
				atomic.AddInt64(&stats.MessagesSent, 1)
				time.Sleep(msgInterval)
			}
			
			fmt.Printf("🔌 [压力-%d] 消息发送完成，断开连接\n", id)
			conn.Close()
			atomic.AddInt64(&stats.TotalDisconnections, 1)
		}(i)
	}
	
	wg.Wait()
}

// 输出最终统计
func printFinalStats(stats *TestStats) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📈 WebSocket压力测试统计报告")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("📡 总连接尝试数: %d\n", stats.TotalConnections)
	fmt.Printf("✅ 成功连接数: %d\n", stats.SuccessConnections)
	fmt.Printf("❌ 失败连接数: %d\n", stats.FailedConnections)
	fmt.Printf("🔌 总断开数: %d\n", stats.TotalDisconnections)
	fmt.Printf("📤 发送消息数: %d\n", stats.MessagesSent)
	fmt.Printf("📥 接收消息数: %d\n", stats.MessagesReceived)
	fmt.Printf("⚠️  错误总数: %d\n", stats.Errors)
	
	successRate := float64(stats.SuccessConnections) / float64(stats.TotalConnections) * 100
	fmt.Printf("📊 连接成功率: %.2f%%\n", successRate)
	
	if stats.Errors == 0 && stats.SuccessConnections == stats.TotalConnections {
		fmt.Println("🎉 测试完美通过！")
	} else {
		fmt.Println("⚠️  测试发现问题，请检查服务器日志")
	}
	
	fmt.Println("\n💡 请检查服务器日志中的连接数变化，特别关注：")
	fmt.Println("   - AddClient 和 RemoveClient 的调用次数是否匹配")
	fmt.Println("   - 是否存在连接泄漏（连接数不归零）")
	fmt.Println("   - 异步操作的执行顺序是否正确")
}
