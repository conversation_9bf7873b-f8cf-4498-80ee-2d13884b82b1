/**
 * 设备详情弹窗模块
 * 
 * 职责: 处理设备详情弹窗的显示、数据填充、状态历史时间线渲染等功能
 * 依赖: config.js, utils.js, event-system.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { DateUtils, StatusUtils, DataUtils } from './utils.js';
import { EVENT_TYPES, emit } from './event-system.js';
import { apiClient } from './api-client.js';

// ==================== 设备详情弹窗类 ====================
/**
 * 设备详情弹窗类
 * 管理设备详情弹窗的显示和数据更新
 */
export class DeviceModal {
    constructor() {
        this.currentDevice = null;
        this.isVisible = false;
        
        // DOM元素缓存
        this.elements = {
            modal: null,
            modalTitle: null,
            modalContent: null,
            closeButton: null,
            backdrop: null
        };
        
        this.initElements();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素引用
     * @private
     */
    initElements() {
        this.elements.modal = document.getElementById('deviceDetailModal');
        this.elements.modalTitle = document.querySelector('#deviceDetailModal h3');
        this.elements.modalContent = document.getElementById('deviceDetailContent');
        this.elements.closeButton = document.querySelector('#deviceDetailModal .btn-close');
        this.elements.backdrop = document.querySelector('#deviceDetailModal .modal-backdrop');
    }

    /**
     * 绑定事件监听器
     * @private
     */
    bindEvents() {
        // 关闭按钮事件
        if (this.elements.closeButton) {
            this.elements.closeButton.addEventListener('click', () => {
                this.hide();
            });
        }

        // 背景点击关闭
        if (this.elements.backdrop) {
            this.elements.backdrop.addEventListener('click', () => {
                this.hide();
            });
        }

        // ESC键关闭
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });

        // 监听设备选择事件
        window.addEventListener(EVENT_TYPES.DEVICE_SELECTED, (event) => {
            this.show(event.detail);
        });

        // 监听设备详情更新事件
        window.addEventListener(EVENT_TYPES.DEVICE_DETAIL_UPDATED, (event) => {
            if (this.isVisible && this.currentDevice && 
                this.currentDevice.imsi === event.detail.imsi) {
                this.updateContent(event.detail.device);
            }
        });
    }

    /**
     * 显示设备详情弹窗
     * @param {Object} device 设备数据
     */
    show(device) {
        if (!device || !this.elements.modal) {
            console.error('[DeviceModal] 无效的设备数据或弹窗元素');
            return;
        }

        this.currentDevice = device;
        this.isVisible = true;

        // 更新弹窗内容
        this.updateContent(device);

        // 显示弹窗
        this.elements.modal.showModal();
        document.body.classList.add('modal-open');

        // 发送显示事件
        emit(EVENT_TYPES.DEVICE_MODAL_SHOWN, {
            imsi: device.imsi,
            timestamp: Date.now()
        });

        console.log(`[DeviceModal] 显示设备详情: ${device.imsi}`);
    }

    /**
     * 隐藏设备详情弹窗
     */
    hide() {
        if (!this.isVisible) return;

        this.isVisible = false;
        
        if (this.elements.modal) {
            this.elements.modal.close();
        }
        
        document.body.classList.remove('modal-open');

        // 发送隐藏事件
        emit(EVENT_TYPES.DEVICE_MODAL_HIDDEN, {
            imsi: this.currentDevice?.imsi,
            timestamp: Date.now()
        });

        console.log('[DeviceModal] 隐藏设备详情弹窗');
        
        // 清空当前设备
        setTimeout(() => {
            this.currentDevice = null;
        }, 300); // 等待动画完成
    }

    /**
     * 更新弹窗内容
     * @param {Object} device 设备数据
     */
    updateContent(device) {
        if (!device || !this.elements.modalContent) return;

        this.currentDevice = device;

        // 更新标题
        if (this.elements.modalTitle) {
            this.elements.modalTitle.textContent = `设备详情 - ${device.imsi}`;
        }

        // 生成弹窗内容HTML
        const contentHTML = this.generateModalContent(device);

        // 更新内容容器
        this.elements.modalContent.innerHTML = contentHTML;

        // 异步加载最近状态变化
        this.loadLatestStatusChange(device.imsi);

        // 初始化任务时间轴
        this.initTaskTimeline(device.imsi);

        console.log(`[DeviceModal] 已更新设备详情: ${device.imsi}`);
    }

    /**
     * 生成弹窗内容HTML
     * @param {Object} device 设备数据
     * @returns {string} HTML内容
     * @private
     */
    generateModalContent(device) {
        return `
            <h3 class="font-bold text-lg mb-4">设备详情 - ${this.escapeHtml(device.imsi)}</h3>

            <!-- 设备基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-base-200">
                    <div class="card-body p-4">
                        <h4 class="card-title text-base">基本信息</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">设备名称:</span>
                                <span class="font-medium">${this.escapeHtml(device.device_name)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">IMSI:</span>
                                <span class="font-medium">${this.escapeHtml(device.imsi)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">当前状态:</span>
                                <span class="badge ${device.current_status === 1 ? 'badge-success' : 'badge-error'} badge-sm">
                                    ${StatusUtils.getStatusText(device.current_status)}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">工作模式:</span>
                                <span class="badge badge-outline badge-sm">
                                    ${StatusUtils.getWorkModeText(device.work_mode)}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-200">
                    <div class="card-body p-4">
                        <h4 class="card-title text-base">设备状态</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">电池电量:</span>
                                <div class="flex items-center gap-2">
                                    <div class="radial-progress text-primary" style="--value:${device.battery_level};" role="progressbar">
                                        <span class="text-xs">${device.battery_level}%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">信号强度:</span>
                                <div class="flex items-center gap-2">
                                    <div class="rating rating-sm">
                                        ${this.renderSignalStrength(device.signal_strength)}
                                    </div>
                                    <span class="text-xs">${device.signal_strength}/5</span>
                                </div>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">温度:</span>
                                <span class="font-medium">${device.temperature}°C</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">固件版本:</span>
                                <span class="font-medium">${this.escapeHtml(device.firmware_version)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 位置信息 -->
            <div class="card bg-base-200 mb-6">
                <div class="card-body p-4">
                    <h4 class="card-title text-base">位置信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">当前位置:</span>
                                <span class="font-medium">${this.escapeHtml(device.position.location_name)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">经纬度:</span>
                                <span class="font-medium">${device.position.latitude.toFixed(4)}, ${device.position.longitude.toFixed(4)}</span>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-base-content/70">当前速度:</span>
                                <span class="font-medium">${device.speed >= 0 ? device.speed + ' km/h' : '未知'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-base-content/70">总里程:</span>
                                <span class="font-medium">${DataUtils.formatNumber(device.mileage, 1)} km</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务信息 -->
            ${device.task_status.task_id > 0 ? `
            <div class="card bg-base-200 mb-6">
                <div class="card-body p-4">
                    <h4 class="card-title text-base">任务信息</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-base-content/70">任务ID:</span>
                            <span class="font-medium">#${device.task_status.task_id}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">任务状态:</span>
                            <span class="badge ${device.task_status.step === 2 ? 'badge-success' : device.task_status.step === 1 ? 'badge-warning' : 'badge-error'} badge-sm">
                                ${StatusUtils.getTaskStatusText(device.task_status.step)}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">进度:</span>
                            <div class="flex items-center gap-2">
                                <progress class="progress progress-primary w-20" value="${device.task_status.progress}" max="100"></progress>
                                <span class="text-xs">${device.task_status.progress}%</span>
                            </div>
                        </div>
                        ${device.task_status.current_station ? `
                        <div class="flex justify-between">
                            <span class="text-base-content/70">路线:</span>
                            <span class="font-medium">${this.escapeHtml(device.task_status.current_station)} → ${this.escapeHtml(device.task_status.target_station)}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
            ` : ''}

            <!-- 主要内容区域：双列布局 -->
            <div class="flex gap-6">
                <!-- 左侧：设备状态历史 -->
                <div class="flex-1">
                    ${this.renderTimeline(device)}
                </div>

                <!-- 右侧：任务时间轴 -->
                <div class="flex-1">
                    <h3 class="font-bold text-lg mb-4">任务时间轴</h3>

                    <!-- 搜索表单 -->
                    <div class="mb-4 p-4 bg-base-200 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="label">
                                    <span class="label-text">选择日期 *</span>
                                </label>
                                <input type="date" id="taskDate" class="input input-bordered w-full" required>
                            </div>
                            <div>
                                <label class="label">
                                    <span class="label-text">任务ID *</span>
                                </label>
                                <input type="number" id="taskId" class="input input-bordered w-full" placeholder="请输入任务ID" required>
                            </div>
                            <div class="flex items-end">
                                <button id="searchTaskTimeline" class="btn btn-primary w-full">搜索</button>
                            </div>
                        </div>
                    </div>

                    <!-- 时间轴展示区域 -->
                    <div id="taskTimelineContainer">
                        <div class="text-center text-gray-500 py-8">
                            请输入搜索条件查看任务时间轴
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 估算设备上一次在线的时间段
     * @param {Object} device 设备数据
     * @returns {Object} 包含在线开始时间和结束时间的对象
     * @private
     */
    estimatePreviousOnlineTime(device) {
        const HEARTBEAT_TIMEOUT = 12 * 1000; // 12秒心跳超时时间
        const lastHeartbeat = new Date(device.last_heartbeat_time);

        // 离线开始时间 = 最后心跳时间 + 心跳超时时间
        const offlineStartTime = new Date(lastHeartbeat.getTime() + HEARTBEAT_TIMEOUT);

        // 估算在线开始时间（简化逻辑：假设设备在最后心跳前2小时开始在线）
        // 在实际应用中，这里应该分析心跳历史记录来确定准确的在线开始时间
        const estimatedOnlineStartTime = new Date(lastHeartbeat.getTime() - (2 * 60 * 60 * 1000)); // 2小时前

        return {
            onlineStartTime: estimatedOnlineStartTime,
            onlineEndTime: lastHeartbeat,
            offlineStartTime: offlineStartTime
        };
    }

    /**
     * 格式化持续时间
     * @param {number} milliseconds 毫秒数
     * @returns {string} 格式化的持续时间
     * @private
     */
    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}天${hours % 24}小时${minutes % 60}分钟`;
        } else if (hours > 0) {
            return `${hours}小时${minutes % 60}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 渲染状态历史时间线
     * @param {Object} device 设备数据
     * @returns {string} 时间线HTML
     * @private
     */
    renderTimeline(device) {
        const HEARTBEAT_TIMEOUT = 12 * 1000; // 12秒心跳超时时间
        const isCurrentlyOnline = device.current_status === 1;
        const currentTime = new Date();
        const lastHeartbeatTime = new Date(device.last_heartbeat_time);

        let timelineItems = '';

        if (!isCurrentlyOnline) {
            // 设备当前离线：显示 在线 -> 离线 的变化过程
            const timeInfo = this.estimatePreviousOnlineTime(device);
            const onlineDuration = timeInfo.onlineEndTime - timeInfo.onlineStartTime;
            const offlineDuration = currentTime - timeInfo.offlineStartTime;

            // 1. 先显示在线状态（上一个状态）
            timelineItems += `
                <li>
                    <div class="timeline-start text-sm text-base-content/70">
                        ${DateUtils.format(timeInfo.onlineStartTime, 'datetime')}
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                             class="w-5 h-5 text-success">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">开始在线</div>
                        <div class="text-sm text-base-content/70">
                            在线时长: ${this.formatDuration(onlineDuration)}
                        </div>
                        <div class="text-xs text-base-content/50">
                            ${DateUtils.format(timeInfo.onlineStartTime, 'datetime')} - ${DateUtils.format(timeInfo.onlineEndTime, 'datetime')}
                        </div>
                    </div>
                    <hr class="bg-success" />
                </li>



                <!-- 2. 再显示离线状态（当前状态） -->
                <li>
                    <div class="timeline-start text-sm text-base-content/70">
                        ${DateUtils.format(timeInfo.offlineStartTime, 'datetime')}
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                             class="w-5 h-5 text-error">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">开始离线</div>
                        <div class="text-sm text-base-content/70">
                            离线时长: ${this.formatDuration(offlineDuration)}
                        </div>
                        <div class="text-xs text-base-content/50">
                            最后心跳: ${DateUtils.format(lastHeartbeatTime, 'datetime')}
                        </div>
                    </div>
                    <hr class="bg-error" />
                </li>`;
        } else {
            // 设备当前在线：显示 离线 -> 在线 的变化过程
            const estimatedOfflineTime = new Date(lastHeartbeatTime.getTime() - (1 * 60 * 60 * 1000)); // 估算1小时前开始离线
            const offlineDuration = lastHeartbeatTime - estimatedOfflineTime;
            const onlineDuration = currentTime - lastHeartbeatTime;

            // 1. 先显示离线状态（上一个状态）
            timelineItems += `
                <li>
                    <div class="timeline-start text-sm text-base-content/70">
                        ${DateUtils.format(estimatedOfflineTime, 'datetime')}
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                             class="w-5 h-5 text-base-content/50">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">离线状态</div>
                        <div class="text-sm text-base-content/70">
                            离线时长: ${this.formatDuration(offlineDuration)}
                        </div>
                    </div>
                    <hr class="bg-base-content/30" />
                </li>



                <!-- 2. 再显示在线状态（当前状态） -->
                <li>
                    <div class="timeline-start text-sm text-base-content/70">
                        ${DateUtils.format(lastHeartbeatTime, 'datetime')}
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                             class="w-5 h-5 text-success">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">恢复在线</div>
                        <div class="text-sm text-base-content/70">
                            在线时长: ${this.formatDuration(onlineDuration)}
                        </div>
                        <div class="text-xs text-base-content/50">
                            最后心跳: ${DateUtils.format(lastHeartbeatTime, 'relative')}
                        </div>
                    </div>
                    <hr class="bg-success" />
                </li>`;
        }

        return `
            <!-- 设备状态历史时间线 -->
            <div>
                <h3 class="font-bold text-lg mb-4">设备状态历史</h3>
                <div class="text-sm text-base-content/60 mb-4">
                    显示最近的状态变化过程 ${isCurrentlyOnline ? '(离线 → 在线)' : '(在线 → 离线)'}
                </div>
                <ul class="timeline timeline-vertical timeline-compact" id="device-timeline-${device.imsi}">
                    ${timelineItems}
                </ul>
            </div>
        `;
    }

    /**
     * 渲染信号强度
     * @param {number} strength 信号强度 (1-5)
     * @returns {string} 信号强度HTML
     * @private
     */
    renderSignalStrength(strength) {
        let html = '';
        for (let i = 1; i <= 5; i++) {
            const filled = i <= strength;
            html += `<input type="radio" class="mask mask-star-2 bg-${filled ? 'warning' : 'base-300'}" disabled />`;
        }
        return html;
    }

    /**
     * HTML转义
     * @param {string} text 文本
     * @returns {string} 转义后的文本
     * @private
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 异步加载最近状态变化
     * @param {string} imsi 设备IMSI
     * @private
     */
    async loadLatestStatusChange(imsi) {
        try {
            const latestChange = await apiClient.getLatestStatusChange(imsi);

            if (latestChange && this.isVisible && this.currentDevice?.imsi === imsi) {
                this.renderLatestStatusChange(imsi, latestChange);
            }
        } catch (error) {
            console.warn(`[DeviceModal] 获取最近状态变化失败: ${imsi}`, error);
            // 静默失败，不影响主要功能
        }
    }

    /**
     * 渲染最近状态变化到时间线
     * @param {string} imsi 设备IMSI
     * @param {Object} statusChange 状态变化数据
     * @private
     */
    renderLatestStatusChange(imsi, statusChange) {
        const placeholder = document.getElementById(`latest-status-change-${imsi}`);
        if (!placeholder) return;

        // 格式化持续时长
        const formatDuration = (seconds) => {
            if (seconds < 60) {
                return `${seconds}秒`;
            } else if (seconds < 3600) {
                return `${Math.floor(seconds / 60)}分钟`;
            } else {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`;
            }
        };

        const statusIcon = statusChange.status === 1 ?
            '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />' :
            '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />';

        const statusColor = statusChange.status === 1 ? 'text-success' : 'text-error';
        const hrColor = statusChange.status === 1 ? 'bg-success' : 'bg-error';

        placeholder.innerHTML = `
            <hr class="${hrColor}" />
            <div class="timeline-start text-sm text-base-content/70">
                ${DateUtils.format(statusChange.timestamp, 'datetime')}
            </div>
            <div class="timeline-middle">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                     class="w-5 h-5 ${statusColor}">
                    ${statusIcon}
                </svg>
            </div>
            <div class="timeline-end timeline-box">
                <div class="font-medium">${statusChange.status_text}</div>
                <div class="text-sm text-base-content/70">
                    ${statusChange.description}
                </div>
                <div class="text-xs text-base-content/50">
                    持续时长: ${formatDuration(statusChange.duration)}
                </div>
            </div>
            <hr class="${hrColor}" />
        `;

        // 显示元素
        placeholder.style.display = 'block';

        console.log(`[DeviceModal] 已渲染最近状态变化: ${imsi} - ${statusChange.status_text}`);
    }

    /**
     * 检查弹窗是否可见
     * @returns {boolean} 是否可见
     */
    isModalVisible() {
        return this.isVisible;
    }

    /**
     * 获取当前显示的设备
     * @returns {Object|null} 当前设备数据
     */
    getCurrentDevice() {
        return this.currentDevice;
    }

    /**
     * 刷新当前设备数据
     */
    refresh() {
        if (this.currentDevice && this.isVisible) {
            // 发送刷新请求事件
            emit(EVENT_TYPES.DEVICE_DETAIL_REFRESH_REQUESTED, {
                imsi: this.currentDevice.imsi,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 初始化任务时间轴
     * @param {string} imsi 设备IMSI
     * @private
     */
    initTaskTimeline(imsi) {
        // 设置默认日期为今天
        const today = new Date().toISOString().split('T')[0];
        const taskDateInput = document.getElementById('taskDate');
        if (taskDateInput) {
            taskDateInput.value = today;
        }

        // 绑定搜索事件
        const searchButton = document.getElementById('searchTaskTimeline');
        if (searchButton) {
            // 移除之前的事件监听器
            searchButton.replaceWith(searchButton.cloneNode(true));
            const newSearchButton = document.getElementById('searchTaskTimeline');

            newSearchButton.addEventListener('click', () => {
                this.searchTaskTimeline(imsi);
            });
        }
    }

    /**
     * 搜索任务时间轴
     * @param {string} imsi 设备IMSI
     * @private
     */
    searchTaskTimeline(imsi) {
        const dateInput = document.getElementById('taskDate');
        const taskIdInput = document.getElementById('taskId');

        if (!dateInput || !taskIdInput) {
            console.error('[DeviceModal] 找不到搜索表单元素');
            return;
        }

        const date = dateInput.value;
        const taskId = taskIdInput.value;

        if (!date || !taskId) {
            this.showTimelineError('请填写完整的搜索条件');
            return;
        }

        // 显示加载状态
        this.showTimelineLoading();

        // 调用API查询时间轴数据
        fetch(`/api/v1/device/task-timeline?imsi=${encodeURIComponent(imsi)}&date=${encodeURIComponent(date)}&taskId=${encodeURIComponent(taskId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    this.renderTaskTimeline(data.data);
                } else {
                    this.showTimelineError(data.message || '查询失败');
                }
            })
            .catch(error => {
                console.error('[DeviceModal] 查询任务时间轴失败:', error);
                this.showTimelineError('查询失败: ' + error.message);
            });
    }

    /**
     * 渲染任务时间轴
     * @param {Object} timelineData 时间轴数据
     * @private
     */
    renderTaskTimeline(timelineData) {
        const container = document.getElementById('taskTimelineContainer');
        if (!container) return;

        if (!timelineData || !timelineData.steps || timelineData.steps.length === 0) {
            container.innerHTML = `
                <div class="text-center text-gray-500 py-8">
                    <div class="text-lg mb-2">📋</div>
                    <div>未找到该任务的时间轴数据</div>
                </div>
            `;
            return;
        }

        const stepDescriptions = {
            0: '任务准备',
            1: '开始执行',
            2: '任务完成',
            3: '任务暂停',
            4: '任务取消'
        };

        let timelineHtml = `
            <div class="mb-4">
                <h4 class="font-semibold text-base">任务 ${timelineData.taskId} 执行时间轴</h4>
                <p class="text-sm text-gray-600">共 ${timelineData.steps.length} 个步骤</p>
            </div>
            <ul class="timeline timeline-vertical">
        `;

        timelineData.steps.forEach((step, index) => {
            const isLast = index === timelineData.steps.length - 1;
            let stepColor = 'timeline-info';

            // 根据step状态设置颜色
            if (step.step === 2) {
                stepColor = 'timeline-success';
            } else if (step.step === 1) {
                stepColor = 'timeline-warning';
            } else if (step.step === 4) {
                stepColor = 'timeline-error';
            }

            timelineHtml += `
                <li>
                    <div class="timeline-start text-xs">${step.time}</div>
                    <div class="timeline-middle">
                        <div class="w-4 h-4 rounded-full bg-current ${stepColor}"></div>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-semibold">${stepDescriptions[step.step] || `步骤 ${step.step}`}</div>
                        <div class="text-sm text-gray-600">Step: ${step.step}</div>
                    </div>
                    ${!isLast ? '<hr class="bg-base-content/30" />' : ''}
                </li>
            `;
        });

        timelineHtml += '</ul>';
        container.innerHTML = timelineHtml;
    }

    /**
     * 显示时间轴加载状态
     * @private
     */
    showTimelineLoading() {
        const container = document.getElementById('taskTimelineContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="text-center py-8">
                <div class="loading loading-spinner loading-md mb-2"></div>
                <div class="text-gray-600">正在查询任务时间轴...</div>
            </div>
        `;
    }

    /**
     * 显示时间轴错误信息
     * @param {string} message 错误信息
     * @private
     */
    showTimelineError(message) {
        const container = document.getElementById('taskTimelineContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="text-center text-error py-8">
                <div class="text-lg mb-2">⚠️</div>
                <div>${this.escapeHtml(message)}</div>
            </div>
        `;
    }
}

// ==================== 全局设备弹窗实例 ====================
export const deviceModal = new DeviceModal();

// ==================== 便捷函数 ====================
/**
 * 显示设备详情弹窗的便捷函数
 * @param {Object} device 设备数据
 */
export const showDeviceDetail = (device) => deviceModal.show(device);

/**
 * 隐藏设备详情弹窗的便捷函数
 */
export const hideDeviceDetail = () => deviceModal.hide();

/**
 * 更新设备详情内容的便捷函数
 * @param {Object} device 设备数据
 */
export const updateDeviceDetail = (device) => deviceModal.updateContent(device);

/**
 * 检查弹窗是否可见的便捷函数
 * @returns {boolean} 是否可见
 */
export const isDeviceDetailVisible = () => deviceModal.isModalVisible();

// ==================== 全局函数（兼容旧代码） ====================
// 这些函数用于兼容HTML中的onclick事件
window.showDeviceDetail = (imsi) => {
    // 如果传入的是IMSI字符串，需要先获取设备数据
    if (typeof imsi === 'string') {
        emit(EVENT_TYPES.DEVICE_DETAIL_REQUESTED, { imsi });
    } else {
        // 如果传入的是设备对象
        deviceModal.show(imsi);
    }
};

window.hideDeviceDetail = () => deviceModal.hide();

// ==================== 默认导出 ====================
export default deviceModal;
