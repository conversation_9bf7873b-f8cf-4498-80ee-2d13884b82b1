/**
 * 事件系统 - 模块间通信
 * 
 * 提供标准化的事件通信机制，用于模块间解耦通信
 * 基于浏览器原生 CustomEvent API
 */

// ==================== 事件常量定义 ====================
export const EVENT_TYPES = {
    // 设备相关事件
    DEVICE_LIST_UPDATED: 'device:list-updated',
    DEVICE_DETAIL_UPDATED: 'device:detail-updated',
    DEVICE_DETAIL_REQUESTED: 'device:detail-requested',
    DEVICE_HISTORY_UPDATED: 'device:history-updated',
    DEVICE_SELECTED: 'device:selected',
    DEVICE_MODAL_SHOWN: 'device:modal-shown',
    DEVICE_MODAL_HIDDEN: 'device:modal-hidden',
    
    // WebSocket相关事件
    WEBSOCKET_CONNECTED: 'websocket:connected',
    WEBSOCKET_DISCONNECTED: 'websocket:disconnected',
    WEBSOCKET_MESSAGE: 'websocket:message',
    WEBSOCKET_ERROR: 'websocket:error',
    
    // UI相关事件
    SEARCH_CHANGED: 'search:changed',
    FILTER_CHANGED: 'filter:changed',
    SORT_CHANGED: 'sort:changed',
    PAGE_CHANGED: 'page:changed',
    
    // 系统相关事件
    APP_INITIALIZED: 'app:initialized',
    LOADING_START: 'system:loading-start',
    LOADING_END: 'system:loading-end',
    ERROR_OCCURRED: 'system:error',
    SUCCESS_MESSAGE: 'system:success'
};

// ==================== 事件管理器 ====================
/**
 * 事件管理器类
 * 提供统一的事件发送和监听接口
 */
export class EventManager {
    constructor() {
        this.listeners = new Map();
        this.eventTarget = window; // 使用window作为事件目标
    }

    /**
     * 发送事件
     * @param {string} eventType 事件类型
     * @param {*} data 事件数据
     * @param {Object} options 事件选项
     */
    emit(eventType, data = null, options = {}) {
        const event = new CustomEvent(eventType, {
            detail: data,
            bubbles: options.bubbles || false,
            cancelable: options.cancelable || false
        });

        console.log(`[EventManager] 发送事件: ${eventType}`, data);
        this.eventTarget.dispatchEvent(event);
    }

    /**
     * 监听事件
     * @param {string} eventType 事件类型
     * @param {Function} handler 事件处理函数
     * @param {Object} options 监听选项
     * @returns {Function} 取消监听的函数
     */
    on(eventType, handler, options = {}) {
        const wrappedHandler = (event) => {
            try {
                handler(event.detail, event);
            } catch (error) {
                console.error(`[EventManager] 事件处理器错误 (${eventType}):`, error);
            }
        };

        this.eventTarget.addEventListener(eventType, wrappedHandler, options);

        // 记录监听器
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, []);
        }
        this.listeners.get(eventType).push({ handler, wrappedHandler });

        console.log(`[EventManager] 注册事件监听: ${eventType}`);

        // 返回取消监听的函数
        return () => this.off(eventType, handler);
    }

    /**
     * 取消事件监听
     * @param {string} eventType 事件类型
     * @param {Function} handler 事件处理函数
     */
    off(eventType, handler) {
        const listeners = this.listeners.get(eventType);
        if (!listeners) return;

        const index = listeners.findIndex(l => l.handler === handler);
        if (index !== -1) {
            const { wrappedHandler } = listeners[index];
            this.eventTarget.removeEventListener(eventType, wrappedHandler);
            listeners.splice(index, 1);
            
            console.log(`[EventManager] 取消事件监听: ${eventType}`);
        }
    }

    /**
     * 一次性事件监听
     * @param {string} eventType 事件类型
     * @param {Function} handler 事件处理函数
     * @returns {Promise} 返回Promise，事件触发时resolve
     */
    once(eventType, handler) {
        return new Promise((resolve) => {
            const onceHandler = (data, event) => {
                handler(data, event);
                resolve(data);
            };
            
            this.on(eventType, onceHandler, { once: true });
        });
    }

    /**
     * 获取事件监听器数量
     * @param {string} eventType 事件类型
     * @returns {number} 监听器数量
     */
    getListenerCount(eventType) {
        const listeners = this.listeners.get(eventType);
        return listeners ? listeners.length : 0;
    }

    /**
     * 清除所有事件监听器
     */
    clear() {
        for (const [eventType, listeners] of this.listeners) {
            listeners.forEach(({ wrappedHandler }) => {
                this.eventTarget.removeEventListener(eventType, wrappedHandler);
            });
        }
        this.listeners.clear();
        console.log('[EventManager] 已清除所有事件监听器');
    }
}

// ==================== 全局事件管理器实例 ====================
export const eventManager = new EventManager();

// ==================== 便捷函数 ====================
/**
 * 发送事件的便捷函数
 * @param {string} eventType 事件类型
 * @param {*} data 事件数据
 */
export const emit = (eventType, data) => eventManager.emit(eventType, data);

/**
 * 监听事件的便捷函数
 * @param {string} eventType 事件类型
 * @param {Function} handler 事件处理函数
 * @returns {Function} 取消监听的函数
 */
export const on = (eventType, handler) => eventManager.on(eventType, handler);

/**
 * 取消事件监听的便捷函数
 * @param {string} eventType 事件类型
 * @param {Function} handler 事件处理函数
 */
export const off = (eventType, handler) => eventManager.off(eventType, handler);

/**
 * 一次性事件监听的便捷函数
 * @param {string} eventType 事件类型
 * @param {Function} handler 事件处理函数
 * @returns {Promise} 返回Promise
 */
export const once = (eventType, handler) => eventManager.once(eventType, handler);

// ==================== 使用示例 ====================
/**
 * 使用示例:
 * 
 * // 导入事件系统
 * import { EVENT_TYPES, emit, on, off } from './event-system.js';
 * 
 * // 监听设备列表更新事件
 * const unsubscribe = on(EVENT_TYPES.DEVICE_LIST_UPDATED, (devices) => {
 *     console.log('设备列表已更新:', devices);
 * });
 * 
 * // 发送设备列表更新事件
 * emit(EVENT_TYPES.DEVICE_LIST_UPDATED, [
 *     { imsi: 'device1', status: 1 },
 *     { imsi: 'device2', status: 0 }
 * ]);
 * 
 * // 取消监听
 * unsubscribe();
 * 
 * // 一次性监听
 * once(EVENT_TYPES.WEBSOCKET_CONNECTED, () => {
 *     console.log('WebSocket已连接');
 * });
 */
