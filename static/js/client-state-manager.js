/**
 * 客户端状态管理器
 * 负责显示和管理WebSocket客户端连接状态
 */

import { wsClient, MESSAGE_TYPE } from './websocket-client.js';
import { on, off } from './event-system.js';

class ClientStateManager {
    constructor() {
        this.isVisible = false;
        this.currentSessionId = null;
        this.clientStates = [];
        this.refreshInterval = null;
        
        this.init();
    }

    /**
     * 初始化客户端状态管理器
     */
    init() {
        this.bindEvents();
        this.setupAutoRefresh();
        console.log('[ClientStateManager] 客户端状态管理器已初始化');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 切换面板显示/隐藏
        const toggleBtn = document.getElementById('toggleClientPanel');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.togglePanel());
        }

        // 关闭面板
        const closeBtn = document.getElementById('closeClientPanel');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hidePanel());
        }

        // 刷新客户端状态
        const refreshBtn = document.getElementById('refreshClientStates');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.requestClientStates());
        }

        // 监听WebSocket消息
        on('websocket:message', (data) => {
            if (data.type === MESSAGE_TYPE.CLIENT_STATES_UPDATE) {
                this.handleClientStatesUpdate(data.data);
            }
        });

        // 监听WebSocket连接状态
        on('websocket:connected', () => {
            this.requestClientStates();
        });
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 每30秒自动刷新一次客户端状态
        this.refreshInterval = setInterval(() => {
            if (wsClient.isConnected()) {
                this.requestClientStates();
            }
        }, 30000);
    }

    /**
     * 请求客户端状态
     */
    requestClientStates() {
        if (wsClient.isConnected()) {
            wsClient.send(MESSAGE_TYPE.GET_CLIENT_STATES);
        }
    }

    /**
     * 处理客户端状态更新
     */
    handleClientStatesUpdate(data) {
        console.log('[ClientStateManager] 🔄 收到客户端状态更新数据:', {
            rawData: data,
            timestamp: new Date().toISOString()
        });

        const previousCount = this.clientStates.length;
        const newCount = data.total_clients || 0;

        this.clientStates = data.client_states || [];

        console.log('[ClientStateManager] 📊 客户端状态变化:', {
            previousCount: previousCount,
            newCount: newCount,
            change: newCount - previousCount,
            clientStates: this.clientStates,
            timestamp: new Date().toISOString()
        });

        this.updateClientCount(newCount);
        this.updateClientStatesList();

        console.log('[ClientStateManager] ✅ 客户端状态更新完成:', {
            totalClients: newCount,
            displayedCount: document.getElementById('clientCount')?.textContent,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 更新客户端数量显示
     */
    updateClientCount(count) {
        console.log('[ClientStateManager] 🎯 开始更新客户端数量显示:', {
            newCount: count,
            timestamp: new Date().toISOString()
        });

        const clientCountElement = document.getElementById('clientCount');
        if (clientCountElement) {
            const oldText = clientCountElement.textContent;
            const oldClassName = clientCountElement.className;

            clientCountElement.textContent = `${count} 个`;

            // 根据连接数量改变颜色
            clientCountElement.className = 'badge ' + (count > 0 ? 'badge-success' : 'badge-warning');

            console.log('[ClientStateManager] 📱 主要客户端数量UI已更新:', {
                elementId: 'clientCount',
                oldText: oldText,
                newText: clientCountElement.textContent,
                oldClassName: oldClassName,
                newClassName: clientCountElement.className,
                timestamp: new Date().toISOString()
            });
        } else {
            console.warn('[ClientStateManager] ⚠️ 主要客户端数量元素未找到:', {
                elementId: 'clientCount',
                timestamp: new Date().toISOString()
            });
        }

        const totalClientsElement = document.getElementById('totalClients');
        if (totalClientsElement) {
            const oldValue = totalClientsElement.textContent;
            totalClientsElement.textContent = count;

            console.log('[ClientStateManager] 📊 面板客户端数量UI已更新:', {
                elementId: 'totalClients',
                oldValue: oldValue,
                newValue: count,
                timestamp: new Date().toISOString()
            });
        } else {
            console.warn('[ClientStateManager] ⚠️ 面板客户端数量元素未找到:', {
                elementId: 'totalClients',
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 更新客户端状态列表
     */
    updateClientStatesList() {
        const listElement = document.getElementById('clientStatesList');
        if (!listElement) return;

        if (this.clientStates.length === 0) {
            listElement.innerHTML = `
                <div class="text-center py-4 text-base-content/50">
                    <i class="fas fa-users text-2xl mb-2"></i>
                    <p>暂无客户端连接</p>
                </div>
            `;
            return;
        }

        listElement.innerHTML = this.clientStates.map(client => {
            const isCurrentClient = client.session_id === this.currentSessionId;
            const filterText = this.formatFilters(client.filters);
            
            return `
                <div class="card bg-base-200 p-3 ${isCurrentClient ? 'ring-2 ring-primary' : ''}">
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex-1">
                            <div class="font-mono text-xs truncate" title="${client.session_id}">
                                ${client.session_id.substring(0, 8)}...
                                ${isCurrentClient ? '<span class="badge badge-primary badge-xs ml-1">当前</span>' : ''}
                            </div>
                            <div class="text-xs text-base-content/70">
                                连接时长: ${client.duration}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-semibold">
                                第${client.current_page}页
                            </div>
                            <div class="text-xs text-base-content/70">
                                ${client.page_size}条/页
                            </div>
                        </div>
                    </div>
                    
                    ${filterText ? `
                        <div class="text-xs text-base-content/60 mt-1">
                            <i class="fas fa-filter mr-1"></i>
                            ${filterText}
                        </div>
                    ` : ''}
                    
                    <div class="text-xs text-base-content/50 mt-1">
                        最后更新: ${this.formatTime(client.last_update)}
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * 格式化过滤条件
     */
    formatFilters(filters) {
        if (!filters || Object.keys(filters).length === 0) {
            return '';
        }

        const parts = [];
        if (filters.status && filters.status !== 'all') {
            parts.push(`状态:${filters.status}`);
        }
        if (filters.search && filters.search.trim()) {
            parts.push(`搜索:${filters.search.trim()}`);
        }

        return parts.join(', ');
    }

    /**
     * 格式化时间
     */
    formatTime(timeStr) {
        try {
            const date = new Date(timeStr);
            return date.toLocaleTimeString('zh-CN', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (e) {
            return timeStr;
        }
    }

    /**
     * 设置当前会话ID
     */
    setCurrentSessionId(sessionId) {
        this.currentSessionId = sessionId;
        
        const currentSessionElement = document.getElementById('currentSession');
        if (currentSessionElement) {
            currentSessionElement.textContent = sessionId ? sessionId.substring(0, 8) + '...' : '--';
            currentSessionElement.title = sessionId || '';
        }
        
        // 更新列表显示
        this.updateClientStatesList();
    }

    /**
     * 切换面板显示状态
     */
    togglePanel() {
        if (this.isVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }

    /**
     * 显示面板
     */
    showPanel() {
        const panel = document.getElementById('clientStatePanel');
        if (panel) {
            panel.classList.remove('hidden');
            this.isVisible = true;
            
            // 立即请求最新状态
            this.requestClientStates();
        }
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
        const panel = document.getElementById('clientStatePanel');
        if (panel) {
            panel.classList.add('hidden');
            this.isVisible = false;
        }
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
        
        console.log('[ClientStateManager] 客户端状态管理器已销毁');
    }
}

// 创建全局实例
const clientStateManager = new ClientStateManager();

export { clientStateManager };
