/**
 * WebSocket客户端模块
 * 
 * 职责: 管理WebSocket连接，处理实时消息推送
 * 依赖: config.js, utils.js, event-system.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { WEBSOCKET_CONFIG } from './config.js';
import { DOMUtils } from './utils.js';
import { EVENT_TYPES, emit } from './event-system.js';

// ==================== WebSocket连接状态 ====================
export const CONNECTION_STATE = {
    DISCONNECTED: 'disconnected',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    RECONNECTING: 'reconnecting',
    ERROR: 'error'
};

// ==================== WebSocket消息类型 ====================
export const MESSAGE_TYPE = {
    // 客户端发送的消息类型
    GET_DEVICE_LIST: 'get_device_list',
    GET_CLIENT_STATES: 'get_client_states',
    CLIENT_STATE_UPDATE: 'client_state_update',
    PING: 'ping',

    // 服务端发送的消息类型
    DEVICE_LIST_UPDATE: 'device_list_update',
    DEVICE_DETAIL_UPDATE: 'device_detail_update',
    DEVICE_HISTORY_UPDATE: 'device_history_update',
    SYSTEM_STATUS_UPDATE: 'system_status_update',
    CLIENT_STATES_UPDATE: 'client_states_update',
    PONG: 'pong'
};

// ==================== WebSocket客户端类 ====================
/**
 * WebSocket客户端类
 * 提供WebSocket连接管理和消息处理功能
 */
export class WebSocketClient {
    constructor() {
        this.url = WEBSOCKET_CONFIG.URL;
        this.config = WEBSOCKET_CONFIG.CONNECTION;



        // 连接状态
        this.state = CONNECTION_STATE.DISCONNECTED;
        this.websocket = null;

        // 重连相关
        this.reconnectAttempts = 0;
        this.reconnectTimer = null;
        this.currentReconnectDelay = this.config.INITIAL_RECONNECT_DELAY;

        // 心跳相关
        this.heartbeatTimer = null;
        this.lastPongTime = 0;

        // 消息处理器
        this.messageHandlers = new Map();

        // 连接状态监听器
        this.stateListeners = new Set();

        // 初始化默认消息处理器
        this.initDefaultHandlers();
    }



    /**
     * 初始化默认消息处理器
     * @private
     */
    initDefaultHandlers() {
        this.addMessageHandler(MESSAGE_TYPE.DEVICE_LIST_UPDATE, (data) => {
            // 标记这是WebSocket实时更新，不是初始加载
            emit(EVENT_TYPES.DEVICE_LIST_UPDATED, { ...data, isRealtimeUpdate: true });
        });

        this.addMessageHandler(MESSAGE_TYPE.DEVICE_DETAIL_UPDATE, (data) => {
            emit(EVENT_TYPES.DEVICE_DETAIL_UPDATED, data);
        });

        this.addMessageHandler(MESSAGE_TYPE.DEVICE_HISTORY_UPDATE, (data) => {
            emit(EVENT_TYPES.DEVICE_HISTORY_UPDATED, data);
        });

        this.addMessageHandler(MESSAGE_TYPE.SYSTEM_STATUS_UPDATE, (data) => {
            emit(EVENT_TYPES.SYSTEM_STATUS_UPDATE, data);
        });

        this.addMessageHandler(MESSAGE_TYPE.CLIENT_STATES_UPDATE, (data) => {
            // 客户端状态更新消息会通过事件系统传递给ClientStateManager
            console.log('[WebSocketClient] 🔄 收到客户端状态更新:', {
                data: data,
                timestamp: new Date().toISOString()
            });

            // 触发websocket:message事件，让ClientStateManager能够接收到消息
            emit(EVENT_TYPES.WEBSOCKET_MESSAGE, {
                type: MESSAGE_TYPE.CLIENT_STATES_UPDATE,
                data: data
            });
        });

        this.addMessageHandler(MESSAGE_TYPE.PONG, () => {
            this.lastPongTime = Date.now();
        });
    }

    /**
     * 连接WebSocket
     * @returns {Promise<void>} 连接Promise
     */
    async connect() {
        if (this.state === CONNECTION_STATE.CONNECTED ||
            this.state === CONNECTION_STATE.CONNECTING) {
            return;
        }

        this.setState(CONNECTION_STATE.CONNECTING);
        console.log(`[WebSocketClient] 正在连接到: ${this.url}`);

        try {
            // 检查WebSocket支持
            if (!window.WebSocket) {
                throw new Error('浏览器不支持WebSocket');
            }

            console.log(`[WebSocketClient] 尝试连接到: ${this.url}`);
            console.log(`[WebSocketClient] 连接超时设置: ${this.config.CONNECTION_TIMEOUT}ms`);
            console.log(`[WebSocketClient] 浏览器WebSocket支持: ${!!window.WebSocket}`);
            console.log(`[WebSocketClient] 当前页面协议: ${window.location.protocol}`);
            console.log(`[WebSocketClient] 当前页面主机: ${window.location.host}`);

            this.websocket = new WebSocket(this.url);

            // 设置连接超时
            const connectTimeout = setTimeout(() => {
                if (this.state === CONNECTION_STATE.CONNECTING) {
                    console.log(`[WebSocketClient] 连接超时，关闭连接`);
                    this.websocket.close();
                    this.handleError(new Error('连接超时'));
                }
            }, this.config.CONNECTION_TIMEOUT);

            // 绑定事件处理器
            this.websocket.onopen = (event) => {
                clearTimeout(connectTimeout);
                this.handleOpen(event);
            };

            this.websocket.onmessage = (event) => {
                this.handleMessage(event);
            };

            this.websocket.onclose = (event) => {
                console.log(`[WebSocketClient] WebSocket关闭事件:`, {
                    code: event.code,
                    reason: event.reason,
                    wasClean: event.wasClean
                });
                clearTimeout(connectTimeout);
                this.handleClose(event);
            };

            this.websocket.onerror = (event) => {
                console.log(`[WebSocketClient] WebSocket错误事件:`, event);
                clearTimeout(connectTimeout);
                this.handleError(event);
            };

        } catch (error) {
            this.handleError(error);
        }
    }

    /**
     * 断开WebSocket连接
     */
    disconnect() {
        console.log('[WebSocketClient] 主动断开连接');
        
        // 清除重连定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        // 清除心跳定时器
        this.stopHeartbeat();
        
        // 关闭连接
        if (this.websocket) {
            this.websocket.close(1000, '主动断开');
            this.websocket = null;
        }
        
        this.setState(CONNECTION_STATE.DISCONNECTED);
    }

    /**
     * 发送消息
     * @param {string} type 消息类型
     * @param {*} data 消息数据
     */
    send(type, data = null) {
        if (this.state !== CONNECTION_STATE.CONNECTED) {
            console.warn('[WebSocketClient] 连接未建立，无法发送消息');
            return false;
        }

        try {
            const message = JSON.stringify({ type, data });
            this.websocket.send(message);
            console.log(`[WebSocketClient] 发送消息: ${message}`);
            return true;
        } catch (error) {
            console.error('[WebSocketClient] 发送消息失败:', error);
            return false;
        }
    }

    /**
     * 添加消息处理器
     * @param {string} type 消息类型
     * @param {Function} handler 处理函数
     */
    addMessageHandler(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, new Set());
        }
        this.messageHandlers.get(type).add(handler);
    }

    /**
     * 移除消息处理器
     * @param {string} type 消息类型
     * @param {Function} handler 处理函数
     */
    removeMessageHandler(type, handler) {
        if (this.messageHandlers.has(type)) {
            this.messageHandlers.get(type).delete(handler);
        }
    }

    /**
     * 添加状态监听器
     * @param {Function} listener 监听函数
     */
    addStateListener(listener) {
        this.stateListeners.add(listener);
    }

    /**
     * 移除状态监听器
     * @param {Function} listener 监听函数
     */
    removeStateListener(listener) {
        this.stateListeners.delete(listener);
    }

    /**
     * 获取当前连接状态
     * @returns {string} 连接状态
     */
    getState() {
        return this.state;
    }

    /**
     * 检查是否已连接
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return this.state === CONNECTION_STATE.CONNECTED;
    }

    // ==================== 私有方法 ====================

    /**
     * 处理连接打开事件
     * @param {Event} event 事件对象
     * @private
     */
    handleOpen(event) {
        const timestamp = Date.now();
        console.log('[WebSocketClient] 🟢 连接已建立:', {
            url: this.url,
            timestamp: timestamp,
            timestampISO: new Date(timestamp).toISOString(),
            event: event
        });

        this.setState(CONNECTION_STATE.CONNECTED);
        this.reconnectAttempts = 0;
        this.currentReconnectDelay = this.config.INITIAL_RECONNECT_DELAY;

        // 初始化心跳时间
        this.lastPongTime = timestamp;

        // 启动心跳
        this.startHeartbeat();

        // 发送连接成功事件
        emit(EVENT_TYPES.WEBSOCKET_CONNECTED, {
            timestamp: timestamp,
            url: this.url
        });

        console.log('[WebSocketClient] 📡 WebSocket连接成功事件已发送');

        // 连接成功后请求设备列表和客户端状态
        setTimeout(() => {
            console.log('[WebSocketClient] 🔄 连接后自动请求数据');
            // 请求数据
            this.send(MESSAGE_TYPE.GET_DEVICE_LIST);
            this.send(MESSAGE_TYPE.GET_CLIENT_STATES);
        }, 100); // 稍微延迟一下，确保连接完全建立
    }

    /**
     * 处理消息接收事件
     * @param {MessageEvent} event 消息事件
     * @private
     */
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            const { type, data } = message;

            console.log(`[WebSocketClient] 收到消息: ${type}`, data);

            // 触发对应的消息处理器
            if (this.messageHandlers.has(type)) {
                const handlers = this.messageHandlers.get(type);
                console.log(`[WebSocketClient] 找到 ${handlers.size} 个处理器处理消息: ${type}`);
                handlers.forEach(handler => {
                    try {
                        handler(data);
                    } catch (error) {
                        console.error(`[WebSocketClient] 消息处理器错误 (${type}):`, error);
                    }
                });
            } else {
                console.warn(`[WebSocketClient] 没有找到处理器处理消息: ${type}`);
            }

            // 发送通用消息事件
            emit(EVENT_TYPES.WEBSOCKET_MESSAGE, { type, data });

        } catch (error) {
            console.error('[WebSocketClient] 消息解析失败:', error, event.data);
        }
    }

    /**
     * 处理连接关闭事件
     * @param {CloseEvent} event 关闭事件
     * @private
     */
    handleClose(event) {
        const timestamp = Date.now();
        console.log('[WebSocketClient] 🔴 连接已关闭:', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
            timestamp: timestamp,
            timestampISO: new Date(timestamp).toISOString(),
            currentState: this.state
        });

        this.websocket = null;
        this.stopHeartbeat();

        // 发送断开连接事件
        emit(EVENT_TYPES.WEBSOCKET_DISCONNECTED, {
            code: event.code,
            reason: event.reason,
            timestamp: timestamp
        });

        console.log('[WebSocketClient] 📡 WebSocket断开连接事件已发送');

        // 如果不是主动关闭，尝试重连
        if (event.code !== 1000 && this.state !== CONNECTION_STATE.DISCONNECTED) {
            console.log('[WebSocketClient] 🔄 非主动关闭，准备重连');
            this.attemptReconnect();
        } else {
            console.log('[WebSocketClient] ✋ 主动关闭或已断开，不重连');
            this.setState(CONNECTION_STATE.DISCONNECTED);
        }
    }

    /**
     * 处理错误事件
     * @param {Event|Error} event 错误事件
     * @private
     */
    handleError(event) {
        const error = event instanceof Error ? event : new Error('WebSocket连接错误');
        console.error('[WebSocketClient] 连接错误:', error);
        
        this.setState(CONNECTION_STATE.ERROR);
        
        // 发送错误事件
        emit(EVENT_TYPES.WEBSOCKET_ERROR, {
            error: error.message,
            timestamp: Date.now()
        });
        
        // 尝试重连
        this.attemptReconnect();
    }

    /**
     * 尝试重连
     * @private
     */
    attemptReconnect() {
        if (this.reconnectAttempts >= this.config.MAX_RECONNECT_ATTEMPTS) {
            console.error('[WebSocketClient] 重连次数已达上限，停止重连');
            this.setState(CONNECTION_STATE.DISCONNECTED);
            
            emit(EVENT_TYPES.ERROR_OCCURRED, {
                type: 'websocket_reconnect_failed',
                message: '实时连接已断开，请刷新页面重试'
            });
            
            return;
        }

        this.setState(CONNECTION_STATE.RECONNECTING);
        this.reconnectAttempts++;
        
        console.log(`[WebSocketClient] 尝试重连 (${this.reconnectAttempts}/${this.config.MAX_RECONNECT_ATTEMPTS})`);
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, this.currentReconnectDelay);
        
        // 指数退避算法
        this.currentReconnectDelay = Math.min(
            this.currentReconnectDelay * 2,
            this.config.MAX_RECONNECT_DELAY
        );
    }

    /**
     * 启动心跳
     * @private
     */
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected()) {
                this.send(MESSAGE_TYPE.PING);
                
                // 检查心跳响应
                setTimeout(() => {
                    const now = Date.now();
                    if (now - this.lastPongTime > this.config.HEARTBEAT_INTERVAL * 2) {
                        console.warn('[WebSocketClient] 心跳超时，关闭连接');
                        this.websocket.close();
                    }
                }, 5000);
            }
        }, this.config.HEARTBEAT_INTERVAL);
    }

    /**
     * 停止心跳
     * @private
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * 设置连接状态
     * @param {string} newState 新状态
     * @private
     */
    setState(newState) {
        if (this.state !== newState) {
            const oldState = this.state;
            this.state = newState;
            
            console.log(`[WebSocketClient] 状态变更: ${oldState} -> ${newState}`);
            
            // 通知状态监听器
            this.stateListeners.forEach(listener => {
                try {
                    listener(newState, oldState);
                } catch (error) {
                    console.error('[WebSocketClient] 状态监听器错误:', error);
                }
            });
        }
    }
}

// ==================== 全局WebSocket客户端实例 ====================
export const wsClient = new WebSocketClient();

// ==================== 便捷函数 ====================
/**
 * 连接WebSocket的便捷函数
 * @returns {Promise<void>} 连接Promise
 */
export const connect = () => wsClient.connect();

/**
 * 断开WebSocket的便捷函数
 */
export const disconnect = () => wsClient.disconnect();

/**
 * 发送消息的便捷函数
 * @param {string} type 消息类型
 * @param {*} data 消息数据
 * @returns {boolean} 是否发送成功
 */
export const send = (type, data) => wsClient.send(type, data);

/**
 * 获取连接状态的便捷函数
 * @returns {string} 连接状态
 */
export const getConnectionState = () => wsClient.getState();

/**
 * 检查是否已连接的便捷函数
 * @returns {boolean} 是否已连接
 */
export const isConnected = () => wsClient.isConnected();

// ==================== 默认导出 ====================
export default wsClient;
