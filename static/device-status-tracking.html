<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备状态追踪系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.10/dist/full.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .loading-skeleton { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

        /* 状态指示器动画 */
        .status-indicator {
            position: relative;
            display: inline-block;
        }

        .status-indicator.online::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -8px;
            width: 6px;
            height: 6px;
            background: #10b981;
            border-radius: 50%;
            transform: translateY(-50%);
            animation: pulse-dot 2s infinite;
        }

        .status-indicator.offline::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -8px;
            width: 6px;
            height: 6px;
            background: #ef4444;
            border-radius: 50%;
            transform: translateY(-50%);
            opacity: 0.5;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
            50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
        }

        /* 卡片悬停效果 */
        .stat-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* 表格行悬停效果 */
        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: hsl(var(--b2));
            transform: scale(1.005);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 设备列表表格样式优化 */
        .device-table {
            border-collapse: separate;
            border-spacing: 0;
            height: auto !important;
            max-height: none !important;
        }

        .device-table th {
            font-weight: 600;
            text-align: left;
            padding: 1rem 0.75rem;
            border-bottom: 2px solid hsl(var(--b3));
            background: hsl(var(--b3)) !important;
            color: hsl(var(--bc));
        }

        /* 强制表头背景色，覆盖DaisyUI的table-pin-rows样式 */
        .device-table thead th {
            background-color: hsl(var(--b3)) !important;
            backdrop-filter: none !important;
        }

        /* 更高优先级的选择器，针对所有可能的组合 */
        .device-table.table-pin-rows thead th,
        .table.table-pin-rows thead th,
        .device-table thead th.bg-base-300 {
            background-color: hsl(var(--b3)) !important;
            backdrop-filter: none !important;
        }

        .device-table td {
            padding: 0.875rem 0.75rem;
            border-bottom: 1px solid hsl(var(--b3));
            vertical-align: middle;
        }

        /* 设备状态徽章样式 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .status-badge.online {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-badge.offline {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* 设备信息卡片样式 */
        .device-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .device-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            background: hsl(var(--p));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* 进度环样式优化 */
        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }

        /* 搜索框聚焦效果 */
        .input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 响应式表格 */
        @media (max-width: 768px) {
            .table-responsive-stack {
                display: block;
            }

            .table-responsive-stack thead {
                display: none;
            }

            .table-responsive-stack tbody,
            .table-responsive-stack tr,
            .table-responsive-stack td {
                display: block;
                width: 100%;
            }

            .table-responsive-stack tr {
                border: 1px solid hsl(var(--b3));
                margin-bottom: 1rem;
                padding: 1rem;
                border-radius: 0.5rem;
            }

            .table-responsive-stack td {
                text-align: right;
                padding-left: 50%;
                position: relative;
            }

            .table-responsive-stack td::before {
                content: attr(data-label);
                position: absolute;
                left: 1rem;
                width: 45%;
                text-align: left;
                font-weight: bold;
                color: hsl(var(--bc) / 0.7);
            }
        }

        /* 加载动画优化 */
        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: loading-dots 1.5s infinite;
        }

        @keyframes loading-dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        /* 主题适配 */
        [data-theme="dark"] .stat-card:hover {
            box-shadow: 0 8px 25px rgba(255,255,255,0.1);
        }

        /* 表格容器样式 - 完全禁用垂直滚动 */
        .table-container {
            overflow-y: visible !important;
            max-height: none !important;
            height: auto !important;
            min-height: auto !important;
            display: block !important;
        }

        /* 更强制的样式，覆盖所有可能的高度设置 */
        .table-container,
        .table-container > *,
        .device-table,
        .device-table > *,
        #deviceTable,
        #deviceTable > * {
            height: auto !important;
            max-height: none !important;
            min-height: auto !important;
        }

        /* 确保卡片和卡片主体没有高度限制 */
        .card, .card-body {
            height: auto !important;
            max-height: none !important;
            min-height: auto !important;
            overflow: visible !important;
        }

        /* 强制表格本身也没有高度限制 */
        .device-table, .device-table tbody, .device-table thead {
            height: auto !important;
            max-height: none !important;
            overflow: visible !important;
        }

        /* 滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: hsl(var(--b2));
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: hsl(var(--bc) / 0.3);
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: hsl(var(--bc) / 0.5);
        }

        /* 时间线样式优化 */
        .timeline-custom {
            position: relative;
            padding-left: 2rem;
        }

        .timeline-custom::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: hsl(var(--b3));
        }

        .timeline-item {
            position: relative;
            padding-bottom: 1.5rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.5rem;
            top: 0.25rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: hsl(var(--p));
            border: 2px solid hsl(var(--b1));
        }

        /* 错误状态样式 */
        .error-state {
            text-align: center;
            padding: 2rem;
            color: hsl(var(--bc) / 0.5);
        }

        .error-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: hsl(var(--er));
        }

        /* 成功状态样式 */
        .success-state {
            text-align: center;
            padding: 2rem;
            color: hsl(var(--bc) / 0.7);
        }

        .success-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: hsl(var(--su));
        }

        /* IMSI下拉框样式 */
        #imsiDropdownContainer {
            position: relative;
        }

        #imsiDropdownContainer .dropdown-content {
            max-height: 15rem;
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        #imsiDropdownContainer .dropdown-content.show {
            display: block;
        }
    </style>
</head>
<body class="bg-base-200 min-h-screen">
    <!-- 主容器 -->
    <div class="container mx-auto p-4 max-w-7xl">
        
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                <i class="fas fa-car text-primary"></i>
                设备状态追踪系统
            </h1>
            <div class="text-sm breadcrumbs">
                <ul>
                    <li><a href="#" class="text-primary">首页</a></li>
                    <li>设备管理</li>
                    <li>状态追踪</li>
                </ul>
            </div>
        </div>

        <!-- 搜索过滤区域 -->
        <div class="card bg-base-100 shadow-sm mb-6">
            <div class="card-body">
                <!-- 搜索、过滤、连接状态和重置按钮 -->
                <div class="flex flex-wrap items-start gap-4">
                    <!-- IMSI搜索 -->
                    <div class="form-control min-w-48">
                        <label class="label">
                            <span class="label-text">IMSI搜索</span>
                        </label>
                        <div class="dropdown dropdown-bottom w-full" id="imsiDropdownContainer">
                            <div class="relative">
                                <input type="text" id="searchInput" placeholder="输入IMSI进行搜索..."
                                       class="input input-bordered input-sm w-full pr-16"
                                       autocomplete="off" />
                                <!-- 清除按钮 -->
                                <button type="button" id="clearSearchBtn"
                                        class="btn btn-ghost btn-xs absolute right-8 top-1/2 transform -translate-y-1/2 p-1 min-h-0 h-6 w-6 hidden"
                                        title="清除搜索">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                                <!-- 下拉箭头 -->
                                <svg class="w-4 h-4 absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-50 w-full p-2 shadow-lg max-h-60 overflow-y-auto">
                                <div id="imsiOptionsContainer">
                                    <!-- IMSI选项将通过JavaScript动态生成 -->
                                </div>
                            </ul>
                        </div>
                    </div>

                    <!-- 状态过滤 -->
                    <div class="form-control min-w-32">
                        <label class="label">
                            <span class="label-text">状态过滤</span>
                        </label>
                        <select id="statusFilter" class="select select-bordered select-sm w-full">
                            <option value="">全部状态</option>
                            <option value="1">在线</option>
                            <option value="0">离线</option>
                        </select>
                    </div>

                    <!-- 重置按钮 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text opacity-0">重置</span>
                        </label>
                        <button id="resetFiltersBtn" class="btn btn-primary btn-sm px-6" title="重置所有搜索条件">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>

                    <!-- 连接状态 -->
                    <div class="form-control min-w-32">
                        <label class="label">
                            <span class="label-text">连接状态</span>
                        </label>
                        <div class="flex items-stretch gap-2 h-8">
                            <span id="connectionStatus" class="btn btn-sm btn-warning px-6 pointer-events-none h-full">实时连接</span>
                            <span class="text-sm text-base-content/70 flex items-center h-full px-3 bg-base-200 rounded">实时推送</span>
                        </div>
                    </div>

                    <!-- 在线客户端 -->
                    <div class="form-control min-w-32">
                        <label class="label">
                            <span class="label-text">在线客户端</span>
                        </label>
                        <div class="flex items-stretch gap-2 h-8">
                            <span id="clientCount" class="btn btn-sm btn-info px-6 pointer-events-none h-full">3 个</span>
                            <button id="toggleClientPanel" class="btn btn-ghost btn-sm px-6 h-full">
                                详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计概览区域 -->
        <div id="statistics" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- 总设备数 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">总设备数</p>
                            <p class="text-2xl font-bold" id="totalDevices">
                                <span class="loading loading-dots loading-sm" id="totalDevicesLoading"></span>
                                <span id="totalDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-primary">
                            <i class="fas fa-car"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="text-xs text-base-content/50">设备总数统计</div>
                    </div>
                </div>
            </div>

            <!-- 在线设备 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">在线设备</p>
                            <p class="text-2xl font-bold text-success" id="onlineDevices">
                                <span class="loading loading-dots loading-sm" id="onlineDevicesLoading"></span>
                                <span id="onlineDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-success">
                            <i class="fas fa-wifi"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="badge badge-success badge-sm" id="onlineRate">0%</div>
                    </div>
                </div>
            </div>

            <!-- 离线设备 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">离线设备</p>
                            <p class="text-2xl font-bold text-error" id="offlineDevices">
                                <span class="loading loading-dots loading-sm" id="offlineDevicesLoading"></span>
                                <span id="offlineDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-error">
                            <i class="fas fa-wifi" style="opacity: 0.3;"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="badge badge-error badge-sm" id="offlineRate">0%</div>
                    </div>
                </div>
            </div>

            <!-- 异常设备 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">异常设备</p>
                            <p class="text-2xl font-bold text-warning" id="errorDevices">
                                <span class="loading loading-dots loading-sm" id="errorDevicesLoading"></span>
                                <span id="errorDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="badge badge-warning badge-sm">需关注</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="card bg-base-100 shadow-sm mb-6">
            <div class="card-body">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="card-title">设备状态列表</h2>
                    <div class="text-sm text-base-content/70">
                        最后更新: <span id="lastUpdateTime">--</span>
                    </div>
                </div>

                <!-- 表格容器 -->
                <div class="table-container custom-scrollbar">
                    <table id="deviceTable" class="table table-zebra table-sm device-table">
                        <thead class="bg-base-300">
                            <tr class="border-b-2 border-base-300">
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">
                                    <div class="flex items-center gap-2">
                                        <span>设备信息</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer hover:opacity-100" onclick="sortTable('imsi')"></i>
                                    </div>
                                </th>
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">
                                    <div class="flex items-center gap-2">
                                        <span>状态</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer hover:opacity-100" onclick="sortTable('status')"></i>
                                    </div>
                                </th>
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">
                                    <div class="flex items-center gap-2">
                                        <span>最后心跳</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer hover:opacity-100" onclick="sortTable('heartbeat')"></i>
                                    </div>
                                </th>
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">位置信息</th>
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">
                                    <div class="flex items-center gap-2">
                                        <span>电池</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer hover:opacity-100" onclick="sortTable('battery')"></i>
                                    </div>
                                </th>
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">工作模式</th>
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">信号强度</th>
                                <th class="text-base-content font-semibold" style="background-color: oklch(92.4169% 0.00108 197.137559) !important;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="deviceTableBody">
                            <!-- 数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-8 hidden">
                    <span class="loading loading-spinner loading-lg text-primary"></span>
                    <p class="mt-2 text-base-content/70">正在加载设备数据...</p>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-8 hidden">
                    <i class="fas fa-inbox text-6xl text-base-content/30 mb-4"></i>
                    <p class="text-base-content/70 mb-2">暂无设备数据</p>
                    <p class="text-sm text-base-content/50">请检查搜索条件或稍后重试</p>
                </div>

                <!-- 错误状态 -->
                <div id="errorState" class="error-state hidden">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p class="text-lg font-semibold mb-2">数据加载失败</p>
                    <p class="text-sm mb-4">请检查网络连接或稍后重试</p>
                    <button class="btn btn-primary btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        重新加载
                    </button>
                </div>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="flex justify-between items-center">
            <div class="text-sm text-base-content/70">
                显示 <span id="pageInfo">1-20</span> 条，共 <span id="totalCount">0</span> 条记录
            </div>
            
            <div id="pagination" class="join">
                <!-- 分页按钮将动态生成 -->
            </div>

            <div class="form-control">
                <select id="pageSize" class="select select-bordered select-sm">
                    <option value="10" selected>10条/页</option>
                    <option value="20">20条/页</option>
                    <option value="50">50条/页</option>
                    <option value="100">100条/页</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 客户端状态面板 -->
    <div id="clientStatePanel" class="fixed bottom-4 right-4 w-96 bg-base-100 shadow-xl rounded-lg border border-base-300 z-50 hidden">
        <div class="p-4">
            <div class="flex justify-between items-center mb-3">
                <h3 class="font-bold text-lg">客户端连接状态</h3>
                <div class="flex items-center gap-2">
                    <button id="refreshClientStates" class="btn btn-ghost btn-xs">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button id="closeClientPanel" class="btn btn-ghost btn-xs">✕</button>
                </div>
            </div>

            <div class="mb-3">
                <div class="stats stats-horizontal w-full">
                    <div class="stat">
                        <div class="stat-title text-xs">总连接数</div>
                        <div id="totalClients" class="stat-value text-lg">0</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title text-xs">当前会话</div>
                        <div id="currentSession" class="stat-value text-xs truncate">--</div>
                    </div>
                </div>
            </div>

            <div class="max-h-64 overflow-y-auto">
                <div id="clientStatesList" class="space-y-2">
                    <!-- 客户端状态列表将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 设备详情弹窗 -->
    <dialog id="deviceDetailModal" class="modal">
        <div class="modal-box w-11/12 max-w-7xl">
            <form method="dialog">
                <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
            </form>
            
            <h3 class="font-bold text-lg mb-4">设备详情</h3>
            
            <!-- 弹窗内容将通过JavaScript动态填充 -->
            <div id="deviceDetailContent">
                <!-- 详情内容 -->
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button>close</button>
        </form>
    </dialog>

    <!-- JavaScript 模块化架构 -->
    <!-- 注意：使用ES6模块，需要type="module" -->
    <script type="module">
        // 导入主控制器模块
        // 主控制器会自动导入和初始化所有其他模块
        import './js/main.js?v=20250802232000';

        console.log('[HTML] 模块化JavaScript架构已加载');


    </script>

    <!-- 备用脚本：如果浏览器不支持ES6模块 -->
    <script nomodule>
        console.error('[HTML] 您的浏览器不支持ES6模块，请升级到现代浏览器');
        document.body.innerHTML = `
            <div class="hero min-h-screen bg-base-200">
                <div class="hero-content text-center">
                    <div class="max-w-md">
                        <h1 class="text-5xl font-bold">浏览器不兼容</h1>
                        <p class="py-6">您的浏览器不支持现代JavaScript功能，请升级到最新版本的Chrome、Firefox、Safari或Edge浏览器。</p>
                        <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                    </div>
                </div>
            </div>
        `;
    </script>
</body>
</html>
