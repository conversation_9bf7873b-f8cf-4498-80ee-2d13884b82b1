#!/bin/bash

# WebSocket死锁快速测试脚本
echo "🚀 开始WebSocket死锁快速测试..."

# 检查服务器是否运行
if ! curl -s http://localhost:8080/api/v1/health > /dev/null; then
    echo "❌ 服务器未运行，请先启动服务器"
    exit 1
fi

echo "✅ 服务器运行正常"

# 编译测试程序
echo "🔨 编译测试程序..."
if ! go build -o test_websocket_deadlock test_websocket_deadlock.go; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 运行测试
echo "🧪 运行并发测试..."
echo "   - 这将创建50个并发WebSocket连接"
echo "   - 每个连接会发送各种类型的消息"
echo "   - 测试时长30秒"
echo "   - 如果出现死锁，程序会卡住"
echo ""

# 设置超时，如果测试超过60秒就认为可能死锁了
timeout 60s ./test_websocket_deadlock

exit_code=$?

if [ $exit_code -eq 124 ]; then
    echo ""
    echo "❌ 测试超时！可能存在死锁问题"
    echo "   请检查服务器日志和goroutine状态"
    exit 1
elif [ $exit_code -eq 0 ]; then
    echo ""
    echo "🎉 测试完成！未发现死锁问题"
    echo "   所有连接正常建立和断开"
    echo "   消息发送和接收正常"
else
    echo ""
    echo "⚠️  测试异常退出，退出码: $exit_code"
    echo "   请检查错误信息"
    exit $exit_code
fi

# 清理
rm -f test_websocket_deadlock

echo "✅ 测试完成，临时文件已清理"
